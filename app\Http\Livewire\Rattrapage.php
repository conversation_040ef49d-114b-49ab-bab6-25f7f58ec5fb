<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Rattrapage extends Component
{
    public $currentPage = PAGELIST;

    public $newRattrapages = [];

    public $notes = [];


    public Parcour $current_parcours;
    public Parcour $current_parcours1;
    public Parcour $current_parcours2;
    public $parcour1;
    public $parcour2;
    public Niveau $current_niveau;
    public Semestre $current_semestre;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.deraq.rattrapage.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editSemestre.nom' => 'required',
            ];
        }

        return [
            'newRattrapages.niveau_id' => 'required',
            'newRattrapages.parcour_id' => 'required',
            'newRattrapages.semestre_id' => 'required',
            'newRattrapages.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListRattrapage()
    {
        $this->currentPage = PAGELIST;
        $this->newRattrapages = [];
        $this->notes = [];
    }

    public function goToAddRattrapage()
    {

        $validationAttributes = $this->validate();

        $this->currentPage = PAGECREATEFORM;

        $this->generate($validationAttributes["newRattrapages"]["parcour_id"], $this->parcour1, $this->parcour2, $validationAttributes["newRattrapages"]["niveau_id"], $validationAttributes["newRattrapages"]["semestre_id"], $validationAttributes["newRattrapages"]["annee_universitaire_id"]);

        // foreach (Matiere::whereHas('ue', fn ($q) => $q->whereSemestreId($validationAttributes["newRattrapages"]["semestre_id"])->whereAnneeUniversitaireId($validationAttributes["newRattrapages"]["annee_universitaire_id"])->whereParcourId($validationAttributes["newRattrapages"]["parcour_id"])->whereNiveauId($validationAttributes["newRattrapages"]["niveau_id"])->get()) as $matiere) {
        //     if($matiere->)
        // }


    }


    public function generate($parcour_id, $parcour_id1, $parcour_id2, $niveau_id, $semestre_id, $annee_universitaire_id)
    {
        // $parcours = Parcour::find($parcour_id);
        // $parcours1 = Parcour::find($parcour_id1);
        // $niveau = Niveau::find($niveau_id);
        // $semestre = Semestre::find($semestre_id);
        // $this->currentPage = PAGECREATEFORM;
        // $notes = Note::with(['user', 'ue'])->whereHas('user', fn ($q) => $q->whereParcourId($parcour_id)->whereNiveauId($niveau_id))
        //     ->whereHas('ue', fn ($q) => $q->whereSemestreId($semestre_id))->get();

        $this->current_parcours = Parcour::find($parcour_id);
        if ($parcour_id1 != 0) {
            $this->current_parcours1 = Parcour::find($parcour_id1);
        }
        if ($parcour_id2 != 0) {
            $this->current_parcours2 = Parcour::find($parcour_id2);
        }

        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_semestre = Semestre::find($semestre_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);

        if ($this->parcour1 == 0 && $this->parcour2 == 0) {

            // $this->notes = Semestre::query()
            //                  ->withWhereHas('ue', fn ($q) => $q->with(['matiere','matiere.notesP1','matiere.notesP2','matiere.notesExam','matiere.note.user'])
            //                             ->whereParcourId($parcour_id)
            //                             ->whereNiveauId($niveau_id)
            //                             ->whereAnneeUniversitaireId($annee_universitaire_id)
            //                             ->whereSemestreId($semestre_id))
            //                  ->get();

            // ,'matiere.notesP1','matiere.notesP2','matiere.notesExam'
            // $this->notes = User::query()
            //     ->whereParcourId($parcour_id)
            //     ->whereNiveauId($niveau_id)
            //     ->withWhereHas('notes', fn ($q) => $q->withWhereHas('ue', fn ($q) => $q->whereSemestreId($semestre_id)->whereAnneeUniversitaireId($annee_universitaire_id))
            //                             // ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($this->current_user->id))->withWhereHas('matiere.notesP1', fn ($q) => $q->where('valeur', '<', 5))
            //                             )
            $info = [];

            $users = User::query()
                ->whereHas('notes.ue', fn ($q) => $q->whereParcourId($parcour_id)
                    ->whereNiveauId($niveau_id)
                    ->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get();

            foreach ($users as $user) {
                $info = [];
                $ue = Ue::query()->with(['matiere'])
                    ->whereSemestreId($semestre_id)
                    ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($user->id))
                    ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($user->id))
                    ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($user->id))
                    ->get();

                foreach ($ue as $ec) {
                    $mat = [];
                    $total = 0;
                    for ($i = 0; $i < $ec->matiere->count(); $i++) {
                        $total += $ec->matiere[$i]->moyenne;
                        array_push($mat, ["nom" => $ec->matiere[$i]->nom, "note" => $ec->matiere[$i]->moyenne]);
                    }

                    $moy = $total / $ec->matiere->count();

                    if ($moy < 10 || containsValueLessThanFive($mat)) {
                        array_push($info, ["nom" => $ec->nom, "moy" => $moy, "matiere" => $mat]);
                    }

                    // $info = [$ue, $moy];
                }
                // $n = collect($info);

                // $collection->push(["nom" => $user->nom, "prenom" => $user->prenom, "notes" => $n]);
                // array_push($nom, ["nom" => $user->nom, "prenom" => $user->prenom]);
                if (!empty($info)) {
                    array_push($this->notes, ["nom" => $user->nom, "prenom" => $user->prenom, "ue" => $info]);
                }
                // dd($info);
                // $collection = collect($users->keyBy('id')->pluck('id'))->combine($endresult);
                // dd($collection);

            }


            // $this->notes = $collection->all();
            //     // ->withWhereHas('notes.matiere.notesP1', fn ($q) => $q->where('valeur', '<', 5))
            //     // ->withWhereHas('notes.matiere.notesP2', fn ($q) => $q->whereUserId($userId))
            //     // ->withWhereHas('notes.matiere.notesExam', fn ($q) => $q->whereUserId($userId))
            //     ->get(['id', 'nom', 'prenom']);

            // $this->notes = Ue::query()->with(['matiere','matiere.notesP1','matiere.notesP2','matiere.notesExam','matiere.note.user'])
            // ->whereParcourId($parcour_id)
            // ->whereNiveauId($niveau_id)
            // ->whereSemestreId($semestre_id)
            // ->whereAnneeUniversitaireId($annee_universitaire_id)
            // ->get();
        } else {
            $this->notes = User::whereParcourId($parcour_id)
                ->orWhere('parcour_id', $parcour_id1)
                ->orWhere('parcour_id', $parcour_id2)
                ->whereNiveauId($niveau_id)
                ->whereHas('notes.ue', fn ($q) => $q->whereSemestreId($semestre_id)->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom'])->sortByDesc('moyenne');
        }

        // dd($this->notes);


        // if($parcour_id1 != 0){
        //     foreach ($etus as $etu) {
        //         array_push($this->notes, ["nom" => $etu->nom, "prenom" => $etu->prenom, "moyenne" => $etu->moyenne, "parcours" => $parcours->sigle, "parcours1" => $parcours1->sigle, "niveau" => $niveau->nom, "semestre" => $semestre->nom]);
        //     }
        // }else{
        //     foreach ($etus as $etu) {
        //         array_push($this->notes, ["nom" => $etu->nom, "prenom" => $etu->prenom, "moyenne" => $etu->moyenne, "parcours" => $parcours->sigle, "niveau" => $niveau->nom, "semestre" => $semestre->nom]);
        //     }
        // }

        // dd($collection);



        // $pdf = Pdf::loadView('pdf.Rattrapageat', compact('Rattrapages'));
        // return $pdf->stream('Rattrapageat.pdf');
    }
}
