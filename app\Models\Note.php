<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Note extends Model
{
    use HasFactory, SoftDeletes;
    use \Znck\Eloquent\Traits\BelongsToThrough;

    protected $fillable = [
        'valeur',
        'type_note_id',
        'user_id',
        'matiere_id',
        'history_note_id',
        'observation',
    ];

    public function user(){
        return $this->belongsTo(User::class, "user_id", "id")->select(['id', 'nom', 'prenom']);
    }

    public function historyNote(){
        return $this->belongsTo(HistoryNote::class)->select(['id', 'estModifiable']);
    }

    public function matiere(){
        return $this->belongsTo(Matiere::class);
    }

    public function typeNote(){
        return $this->belongsTo(TypeNote::class);
    }

    public function ue(){
        return $this->belongsToThrough(Ue::class, Matiere::class);
    }
}
