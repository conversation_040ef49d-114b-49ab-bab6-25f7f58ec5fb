<?php

namespace App\Http\Livewire;

use App\Models\Domaine;
use Livewire\Component;


class Domaines extends Component
{
    public $currentPage = PAGELIST;

    public $newDomaine = [];
    public $editDomaine = [];
    
    public function render()
    {
        return view('livewire.deraq.domaine.index', [
            "domaines" => Domaine::all()
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editDomaine.nom' => 'required',
            ];
        }

        return [
            'newDomaine.nom' => 'required',
            
        ];
    }

    public function goToListSemestre()
    {
        $this->currentPage = PAGELIST;
        $this->newDomaine = [];
    }

    public function goToAddSemestre()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditDomaine(Domaine $Domaine){
        $this->editDomaine = $Domaine->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addDomaine()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        Domaine::create($validationAttributes["newDomaine"]);

        $this->newDomaine = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Domaine créé avec succès!"]);
        // dump($pass);
        $this->goToListDomaine();
    }

    public function updateDomaine(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        Domaine::find($this->editDomaine["id"])->update($validationAttributes["editDomaine"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Domaine mis à jour avec succès!"]);
        $this->goToListDomaine();
    }

    public function deleteDomaine($id)
    {
        Domaine::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Domaine supprimé avec succès!"]);
    }
}
