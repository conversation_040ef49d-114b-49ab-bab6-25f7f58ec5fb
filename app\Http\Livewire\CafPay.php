<?php

namespace App\Http\Livewire;

use App\Models\HistoriquePayment;
use Livewire\Component;

class CafPay extends Component
{
    public $currentPage = PAGELIST;
    
    public function render()
    {
        return view('livewire.caf.cafpay.index', [
            "pays" => HistoriquePayment::with(['user', 'payment', 'moyen', 'encaissement'])->whereDate('created_at', now()->today())->get(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }
}
