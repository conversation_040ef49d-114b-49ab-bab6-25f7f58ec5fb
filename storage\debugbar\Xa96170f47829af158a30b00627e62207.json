{"__meta": {"id": "Xa96170f47829af158a30b00627e62207", "datetime": "2025-07-01 17:13:15", "utime": **********.901007, "method": "GET", "uri": "/test-users", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379194.511449, "end": **********.901035, "duration": 1.3895859718322754, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1751379194.511449, "relative_start": 0, "end": **********.62981, "relative_end": **********.62981, "duration": 1.118360996246338, "duration_str": "1.12s", "params": [], "collector": null}, {"label": "Application", "start": **********.631504, "relative_start": 1.****************, "end": **********.901038, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "270ms", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET test-users", "middleware": "web", "uses": "Closure() {#532\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#509 …}\n  file: \"C:\\xampp\\htdocs\\ImsaaFoad\\routes\\web.php\"\n  line: \"68 to 108\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\routes\\web.php&line=68\">\\routes\\web.php:68-108</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02451, "accumulated_duration_str": "24.51ms", "statements": [{"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where `users`.`deleted_at` is null limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\routes\\web.php", "line": 73}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 237}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}], "duration": 0.00741, "duration_str": "7.41ms", "stmt_id": "\\routes\\web.php:73", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 30.233}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (1, 7, 17, 19, 21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\routes\\web.php", "line": 73}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 237}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}], "duration": 0.01106, "duration_str": "11.06ms", "stmt_id": "\\routes\\web.php:73", "connection": "inscriptionimsaa", "start_percent": 30.233, "width_percent": 45.124}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\routes\\web.php", "line": 76}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 237}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\routes\\web.php:76", "connection": "inscriptionimsaa", "start_percent": 75.357, "width_percent": 3.305}, {"sql": "select count(*) as aggregate from `users` where `email` is not null and `password` is not null and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\routes\\web.php", "line": 77}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 237}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\routes\\web.php:77", "connection": "inscriptionimsaa", "start_percent": 78.662, "width_percent": 3.182}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and (`nom` like '%étudiant%' or `nom` like '%Etudiant%')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%&eacute;tudiant%", "%Etudiant%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\routes\\web.php", "line": 80}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 237}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}], "duration": 0.00445, "duration_str": "4.45ms", "stmt_id": "\\routes\\web.php:80", "connection": "inscriptionimsaa", "start_percent": 81.844, "width_percent": 18.156}]}, "models": {"data": {"App\\Models\\Role": 5, "App\\Models\\User": 5}, "count": 10}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "V3v9SxZRB5JoDu90p7tSRAIryO9ZYILtFiIGmfhc", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/test-users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/test-users", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-469502470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469502470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1258502964 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.22621.4391</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258502964\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1571304359 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56420</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/test-users</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/test-users</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/test-users</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"81 characters\">Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.22621.4391</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379194.5114</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379194</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571304359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1945645369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1945645369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-959846441 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:13:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InI3cUpkTVJ2aG1Xa2tScDlab2V3V0E9PSIsInZhbHVlIjoiNGNvNHdSU1ZOc24zVkcrTXdPVjN6VGE5cnBHemllYkFjRnBnQUNyNHRaLzNnTmxMOVV3eGxBWEN0bEpTZFU4b3hoc0UvdnFQUUt2eUFvVGRUd2FubGE2T0F6V3dINGdKYmtiTWtsbi9zWkNPQzg3OEpjVEJmR005a29paVBlTEsiLCJtYWMiOiIyYzRmYmUxMGEyMjhiYzk2NDU5M2M1MWFlN2MyZmM3MjM0OThkYTY3ODNhYjU2YzlmYWVkZjMwOWI0MWQxYjJiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:13:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IjEzUVNHWFJZWmtpM0ZUV3ZRVDlvamc9PSIsInZhbHVlIjoiQjEwK0RBa3VJRTdXeFBLTWFNZ0pGaGNWN1cxK2grdVgwWXZWSDRyZ1pndGRnSXRHSGxlSHQyVFpXVzU5N2h6WnJKWExGbzgwZEc3aUpSbmxKUmFnWGtaMkZ4T0d6NGRTdzVaTFp3dU9DcjR6S042d3lDOTVFV2R2dDNhbzkwbDQiLCJtYWMiOiJiNTg3NGIzYmVjMGY3M2QyZmI1ZTE5NzlmYTE0Y2YwMWZhNzk3YzU4Y2EzYzA4NTZlMDdiM2FjOTc0Y2M1YzBhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:13:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InI3cUpkTVJ2aG1Xa2tScDlab2V3V0E9PSIsInZhbHVlIjoiNGNvNHdSU1ZOc24zVkcrTXdPVjN6VGE5cnBHemllYkFjRnBnQUNyNHRaLzNnTmxMOVV3eGxBWEN0bEpTZFU4b3hoc0UvdnFQUUt2eUFvVGRUd2FubGE2T0F6V3dINGdKYmtiTWtsbi9zWkNPQzg3OEpjVEJmR005a29paVBlTEsiLCJtYWMiOiIyYzRmYmUxMGEyMjhiYzk2NDU5M2M1MWFlN2MyZmM3MjM0OThkYTY3ODNhYjU2YzlmYWVkZjMwOWI0MWQxYjJiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:13:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IjEzUVNHWFJZWmtpM0ZUV3ZRVDlvamc9PSIsInZhbHVlIjoiQjEwK0RBa3VJRTdXeFBLTWFNZ0pGaGNWN1cxK2grdVgwWXZWSDRyZ1pndGRnSXRHSGxlSHQyVFpXVzU5N2h6WnJKWExGbzgwZEc3aUpSbmxKUmFnWGtaMkZ4T0d6NGRTdzVaTFp3dU9DcjR6S042d3lDOTVFV2R2dDNhbzkwbDQiLCJtYWMiOiJiNTg3NGIzYmVjMGY3M2QyZmI1ZTE5NzlmYTE0Y2YwMWZhNzk3YzU4Y2EzYzA4NTZlMDdiM2FjOTc0Y2M1YzBhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:13:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959846441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-676480709 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V3v9SxZRB5JoDu90p7tSRAIryO9ZYILtFiIGmfhc</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/test-users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676480709\", {\"maxDepth\":0})</script>\n"}}