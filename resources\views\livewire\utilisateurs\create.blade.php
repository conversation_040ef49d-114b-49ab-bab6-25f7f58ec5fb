<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-outline-primary me-3" wire:click.prevent="goToListUser()">
                        <i class="fa fa-arrow-left me-1"></i> Retour
                    </button>
                    <i class="fa fa-user-plus me-2 text-primary"></i>
                    Nouvel Utilisateur
                </h1>
                <p class="fs-sm fw-medium text-muted mb-0">
                    Créer un nouveau compte utilisateur avec les informations requises
                </p>
            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">
                            <i class="fa fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Création
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Progress indicator -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="block block-rounded">
                <div class="block-content block-content-full">
                    <div class="progress-wizard">
                        <div class="d-flex justify-content-between">
                            <div class="step active">
                                <div class="step-icon"><i class="fa fa-user"></i></div>
                                <div class="step-title">Informations personnelles</div>
                            </div>
                            <div class="step">
                                <div class="step-icon"><i class="fa fa-envelope"></i></div>
                                <div class="step-title">Contact</div>
                            </div>
                            <div class="step">
                                <div class="step-icon"><i class="fa fa-shield-alt"></i></div>
                                <div class="step-title">Rôles & Permissions</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" role="form" wire:submit.prevent="addUser()" enctype="multipart/form-data">
        <!-- Informations personnelles -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <i class="fa fa-user me-2"></i>Informations personnelles
                </h3>
                <div class="block-options">
                    <span class="badge bg-primary">Étape 1/3</span>
                </div>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-md-6">
                        <label class="form-label" for="nom">
                            Nom <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.nom"
                            class="form-control @error('newUser.nom') is-invalid @enderror"
                            id="nom" placeholder="Entrez le nom de famille">
                        @error('newUser.nom')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="prenom">
                            Prénom <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.prenom"
                            class="form-control @error('newUser.prenom') is-invalid @enderror"
                            id="prenom" placeholder="Entrez le prénom">
                        @error('newUser.prenom')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="date_naissance">
                            Date de naissance <span class="text-danger">*</span>
                        </label>
                        <input type="text" onchange='Livewire.emit("selectDate", this.value)'
                            class="js-datepicker form-control @error('newUser.date_naissance') is-invalid @enderror"
                            id="date_naissance" data-week-start="1" data-autoclose="true"
                            data-today-highlight="true" data-date-format="dd/mm/yy"
                            placeholder="Sélectionnez une date">
                        @error('newUser.date_naissance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="sexe">
                            Sexe <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('newUser.sexe') is-invalid @enderror"
                            wire:model="newUser.sexe" id="sexe">
                            <option value="">Sélectionnez le sexe</option>
                            <option value="H">Homme</option>
                            <option value="F">Femme</option>
                        </select>
                        @error('newUser.sexe')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="lieu_naissance">
                            Lieu de naissance <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.lieu_naissance"
                            class="form-control @error('newUser.lieu_naissance') is-invalid @enderror"
                            id="lieu_naissance" placeholder="Ville de naissance">
                        @error('newUser.lieu_naissance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="nationalite">
                            Nationalité <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.nationalite"
                            class="form-control @error('newUser.nationalite') is-invalid @enderror"
                            id="nationalite" placeholder="Nationalité">
                        @error('newUser.nationalite')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-12">
                        <label class="form-label" for="adresse">
                            Adresse complète <span class="text-danger">*</span>
                        </label>
                        <textarea wire:model.lazy="newUser.adresse"
                            class="form-control @error('newUser.adresse') is-invalid @enderror"
                            id="adresse" rows="3" placeholder="Adresse complète"></textarea>
                        @error('newUser.adresse')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="ville">
                            Ville <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.ville"
                            class="form-control @error('newUser.ville') is-invalid @enderror"
                            id="ville" placeholder="Ville de résidence">
                        @error('newUser.ville')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="pays">
                            Pays <span class="text-danger">*</span>
                        </label>
                        <input type="text" wire:model.lazy="newUser.pays"
                            class="form-control @error('newUser.pays') is-invalid @enderror"
                            id="pays" placeholder="Pays de résidence">
                        @error('newUser.pays')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="cin">
                            Numéro de pièce d'identité
                        </label>
                        <input type="text" wire:model.lazy="newUser.cin"
                            class="form-control @error('newUser.cin') is-invalid @enderror"
                            id="cin" placeholder="CIN, Passeport, etc.">
                        @error('newUser.cin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Informations de contact -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <i class="fa fa-envelope me-2"></i>Informations de contact
                </h3>
                <div class="block-options">
                    <span class="badge bg-primary">Étape 2/3</span>
                </div>
            </div>
            <div class="block-content">
                <div class="row g-4">

                    <div class="col-md-6">
                        <label class="form-label" for="email">
                            Adresse email <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-envelope"></i>
                            </span>
                            <input type="email" wire:model.lazy="newUser.email"
                                class="form-control @error('newUser.email') is-invalid @enderror"
                                id="email" placeholder="<EMAIL>">
                        </div>
                        @error('newUser.email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="telephone1">
                            Téléphone principal <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-phone"></i>
                            </span>
                            <input type="tel" wire:model.lazy="newUser.telephone1"
                                class="form-control @error('newUser.telephone1') is-invalid @enderror"
                                id="telephone1" placeholder="+261 XX XX XXX XX">
                        </div>
                        @error('newUser.telephone1')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="telephone2">
                            Téléphone secondaire
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-phone"></i>
                            </span>
                            <input type="tel" wire:model.lazy="newUser.telephone2"
                                class="form-control" id="telephone2"
                                placeholder="+261 XX XX XXX XX (optionnel)">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rôles et permissions -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <i class="fa fa-shield-alt me-2"></i>Rôles et permissions
                </h3>
                <div class="block-options">
                    <span class="badge bg-primary">Étape 3/3</span>
                </div>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-md-12">
                        <label class="form-label" for="role">
                            Rôle utilisateur <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('newUser.role') is-invalid @enderror"
                            wire:model="newUser.role" id="role">
                            <option value="">Sélectionnez un rôle</option>
                            @foreach ($roles as $role)
                                <option value="{{ $role->id }}">
                                    {{ $role->nom }}
                                    @if($role->description)
                                        - {{ $role->description }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        @error('newUser.role')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            Le rôle détermine les permissions et l'accès de l'utilisateur au système.
                        </div>
                    </div>

                    @if ($isEtu)
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle me-2"></i>
                                <strong>Informations académiques</strong><br>
                                Ces informations sont requises pour les étudiants.
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label" for="parcour_id">
                                Parcours <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('newUser.parcour_id') is-invalid @enderror"
                                wire:model="newUser.parcour_id" id="parcour_id">
                                <option value="">Sélectionnez un parcours</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUser.parcour_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6">
                            <label class="form-label" for="niveau_id">
                                Niveau <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('newUser.niveau_id') is-invalid @enderror"
                                wire:model="newUser.niveau_id" id="niveau_id">
                                <option value="">Sélectionnez un niveau</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUser.niveau_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="block block-rounded">
            <div class="block-content">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary" wire:click.prevent="goToListUser()">
                        <i class="fa fa-times me-1"></i> Annuler
                    </button>
                    <div>
                        <button type="button" class="btn btn-outline-primary me-2" wire:click="resetForm">
                            <i class="fa fa-undo me-1"></i> Réinitialiser
                        </button>
                        <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <i class="fa fa-save me-1"></i> Créer l'utilisateur
                            </span>
                            <span wire:loading>
                                <i class="fa fa-spinner fa-spin me-1"></i> Création en cours...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
                </div>
            </div>
        </div>
    </form>
</div>
