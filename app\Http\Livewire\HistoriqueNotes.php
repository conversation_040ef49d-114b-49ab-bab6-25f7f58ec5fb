<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\TypeNote;
use Livewire\Component;
use Livewire\WithPagination;

class HistoriqueNotes extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editNote = [];
    public $query;
    public $filtreAnnee;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreType;

    public $historyId;

    public function updatingQuery(){
        $this->resetPage();
    }
    public function updatingFiltreAnnee(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }
    public function updatingFiltreType(){
        $this->resetPage();
    }

    public function render()
    {

        $persQuery = HistoryNote::query()->with(['user', 'matieres.ue.parcours', 'types']);

        if($this->query != ""){
            $persQuery->whereHas('matieres', function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if ($this->filtreParcours != "") {
            $persQuery->whereHas('matieres.ue.parcours', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        if ($this->filtreNiveau != "") {
            $persQuery->whereHas('matieres.ue.niveau', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }
        if ($this->filtreType != "") {
            $persQuery->whereTypeNoteId($this->filtreType);
        }

        if($this->filtreAnnee != ""){
            $persQuery->whereHas('matieres.ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }
        
        return view('livewire.deraq.notehistory.index', [
            "histories" => $persQuery->latest()->paginate(10),
            "supNote" => $persQuery->latest()->onlyTrashed()->paginate(10),
            "annees" => AnneeUniversitaire::all(),
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "typenotes" => TypeNote::all(),
            
        ])
        ->extends('layouts.backend')
        ->section('content');

    }

    public function rules()
    {
        return [
            "editNote.*.valeur" => "required|numeric|min:0|max:20",
            "editNote.0.type_note_id" => "required",
        ];
    }

    public function goToEditNote($matiereId, $typeId, $historyId){
        $this->currentPage = PAGEEDITFORM;
        $this->historyId = $historyId;
        $this->editNote = Note::with(['user', 'historyNote'])->whereMatiereId($matiereId)
            ->whereTypeNoteId($typeId)
            ->get()->toArray();
    }

    public function goToListNote()
    {
        $this->currentPage = PAGELIST;
        $this->editNote = [];
    }

    public function goToSuppNote()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function updateNote(){
        $this->validate();
        
        foreach ($this->editNote as $note) {
            Note::find($note["id"])->update([
                "valeur" => $note["valeur"],
                "type_note_id" => $this->editNote[0]["type_note_id"],
            ]);
        }

        HistoryNote::find($this->historyId)->update([
            "type_note_id" => $this->editNote[0]["type_note_id"],
        ]);
        
        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Note mis à jour avec succès!"]);
        $this->currentPage = PAGELIST;
    }

    public function deleteHisto($id)
    {
        HistoryNote::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note supprimé avec succès!"]);
        $this->render();
    }
    public function restoreHisto($id)
    {
        $histo = HistoryNote::onlyTrashed()->findOrFail($id);

        $histo->restore();

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note restoré avec succès!"]);
        $this->currentPage = PAGELIST;
    }
    public function deleteDefHisto($id)
    {
        $histo = HistoryNote::onlyTrashed()->findOrFail($id);

        $histo->forceDelete();

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note supprimé avec succès!"]);
        $this->currentPage = PAGELIST;
    }
}
