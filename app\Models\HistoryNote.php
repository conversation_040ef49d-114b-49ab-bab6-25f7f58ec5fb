<?php

namespace App\Models;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class HistoryNote extends Model
{
    use HasFactory, SoftDeletes, CascadeSoftDeletes;
    use \Znck\Eloquent\Traits\BelongsToThrough;

    protected $fillable = [
        'matiere_id',
        'type_note_id',
        'user_id',
        'history_note_id',
        'is_valid',
        'estModifiable',
        
    ];

    protected $cascadeDeletes = ['note'];

    public function matieres(){
        return $this->belongsTo(Matiere::class, "matiere_id", "id");
    }

    public function types(){
        return $this->belongsTo(TypeNote::class, "type_note_id", "id");
    }

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function note(){
        return $this->hasMany(Note::class);
    }

    public function ue(){
        return $this->belongsToThrough(Ue::class, Matiere::class);
    }
}
