<?php

namespace App\Http\Livewire;

use App\Mail\SignUp;
use App\Models\AnneeUniversitaire;
use App\Models\Mention;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Livewire\WithPagination;

class Utilisateurs extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newUser = [];
    public $editUser = [];
    public $mediaItems = [];
    public $isEtu = false;
    public $query;
    public $newPasswd;

    public $rolePermissions = [];

    protected $listeners = ["selectDate" => 'getSelectedDate'];

    public function updatingQuery(){
        $this->resetPage();
    }

    public function render()
    {
        Carbon::setLocale("fr");
        
        
            if ($this->newUser != []) {
                $this->showParcours();
            }
        

        return view('livewire.utilisateurs.index', [
            "users" => User::where('nom', 'like', '%'. $this->query .'%')
                            ->orWhere('prenom', 'like', '%'. $this->query .'%')
                            ->with("roles")->paginate(10),
            "roles" => Role::all(),
            "parcours" => Parcour::all(['id', 'nom']),
            "mentions" => Mention::all(),
            "niveaux" => Niveau::all(['id', 'nom']),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        $this->editUser = [];
        // return redirect(request()->header('Referer'));
    }

    public function showParcours()
    {
        if ($this->newUser["role"] == 5) {
            $this->isEtu = true;
        }
        elseif ($this->newUser["role"] != 5) {
            $this->isEtu = false;
        }
        
    }


    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUser.nom' => 'required',
                'editUser.prenom' => 'required',
                'editUser.email' => ['required', 'email', Rule::unique("users", "email")->ignore($this->editUser['id'])],
                'editUser.telephone1' => ['required', 'numeric', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])],
                'editUser.sexe' => 'required',
                'editUser.parcour_id' => '',
                'editUser.niveau_id' => '',
            ];
        }

        return [
            'newUser.nom' => 'required',
            'newUser.prenom' => 'required',
            'newUser.email' => 'required|email|unique:users,email',
            'newUser.sexe' => 'required',
            'newUser.matricule' => 'unique:users,matricule',
            'newUser.telephone1' => 'required|numeric|unique:users,telephone1',
            'newUser.date_naissance' => 'required|before:01/01/14',
            'newUser.lieu_naissance' => 'required',
            'newUser.nationalite' => 'required',
            'newUser.ville' => 'required',
            'newUser.pays' => 'required',
            'newUser.adresse' => 'required',
            'newUser.telephone1' => 'required',
            'newUser.cin' => 'unique:users,cin',

        ];
    }
    
    public function goToAddUser()
    {
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->currentPage = PAGECREATEFORM;
    }

    public function goTest()
    {

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur créé avec succès!"]);
    }

    public function goToEditUser($id)
    {
        $user = User::find($id);
        $this->editUser = $user->toArray();
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };
        $this->mediaItems = $user->getMedia("*");

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
        $this->populateRolePermissions();
    }

    public function populateRolePermissions()
    {
        $this->rolePermissions["roles"] = [];
        $this->rolePermissions["permissions"] = [];

        $mapForCB = function ($value) {
            return $value["id"];
        };

        $roleIds = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray()); // [1, 2, 4]
        $permissionIds = array_map($mapForCB, User::find($this->editUser["id"])->permissions->toArray()); // [1, 2, 4]

        foreach (Role::all() as $role) {
            if (in_array($role->id, $roleIds)) {
                array_push($this->rolePermissions["roles"], ["role_id" => $role->id, "role_nom" => $role->nom, "active" => true]);
            } else {
                array_push($this->rolePermissions["roles"], ["role_id" => $role->id, "role_nom" => $role->nom, "active" => false]);
            }
        }

        // foreach (Permission::all() as $permission) {
        //     if (in_array($permission->id, $permissionIds)) {
        //         array_push($this->rolePermissions["permissions"], ["permission_id" => $permission->id, "permission_nom" => $permission->nom, "active" => true]);
        //     } else {
        //         array_push($this->rolePermissions["permissions"], ["permission_id" => $permission->id, "permission_nom" => $permission->nom, "active" => false]);
        //     }
        // }


        // la logique pour charger les roles et les permissions
    }

    public function updateRoleAndPermissions()
    {
        
        DB::table("role_user")->where("user_id", $this->editUser["id"])->delete();
        // DB::table("user_permission")->where("user_id", $this->editUser["id"])->delete();

        foreach ($this->rolePermissions["roles"] as $role) {
            if ($role["active"]) {
                User::find($this->editUser["id"])->roles()->attach($role["role_id"]);
            }
        }

        // foreach ($this->rolePermissions["permissions"] as $permission) {
        //     if ($permission["active"]) {
        //         User::find($this->editUser["id"])->permissions()->attach($permission["permission_id"]);
        //     }
        // }

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Roles et permissions mis à jour avec succès!"]);
    }

    public function getSelectedDate($date)
    {
        $this->newUser["date_naissance"] = $date;
    }

    

    public function addUser()
    {
        $pass = Str::random(10);
        $role = $this->newUser["role"];
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";
        $validationAttributes["newUser"]["password"] = Hash::make($pass);

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        $user = User::create($validationAttributes["newUser"]);

        

        if ($role) {
            $user->roles()->attach($role);
        }

        Mail::to($this->newUser["email"])->send(new SignUp($this->newUser["nom"], $this->newUser["prenom"], $pass, $this->newUser["email"]));

        $this->newUser = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur créé avec succès!"]);
        // dump($pass);
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        $user = User::find($this->editUser["id"]);
        $user->update($validationAttributes["editUser"]);


        $user->info()->updateOrCreate([
            "parcour_id" => $this->editUser['parcour_id'],
            "niveau_id" => $this->editUser['niveau_id'],
            "annee_universitaire_id" => $this->editUser['annee_universitaire_id'],
        ]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur mis à jour avec succès!"]);
    }

    public function confirmDelete($name, $id)
    {
        $this->dispatchBrowserEvent("showConfirmMessage", ["message" => [
            "text" => "Vous êtes sur le point de supprimer $name de la liste des utilisateurs. Voulez-vous continuer?",
            "title" => "Êtes-vous sûr de continuer?",
            "type" => "warning",
            "data" => [
                "user_id" => $id
            ]
        ]]);
    }

    public function deleteUser($id)
    {
        User::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur supprimé avec succès!"]);
    }

    public function resetPassword(){

        User::find($this->editUser["id"])->update(["password" => Hash::make($this->newPasswd)]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Mot de passe utilisateur réinitialisé avec succès!"]);
    }
}
