<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Parcour extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nom',
        'sigle',
        'mention_id',
        
    ];

    public function etu(){
        return $this->hasMany(User::class);
    }

    public function mention(){
        return $this->belongsTo(Mention::class);
    }

    public function matiere(){
        return $this->hasMany(Matiere::class);
    }

    
}
