<?php

namespace App\Http\Livewire;

use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\TypeNote;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class CoursEnseignant extends Component
{
    public $currentPage = PAGELIST;
    use WithPagination;
    protected $paginationTheme = "bootstrap";


    public $selectedCours;



    public function render()
    {

        return view('livewire.enseignant.cours.index', [
            "cours" => Matiere::with(['user', 'ue'])
                ->whereUserId(auth()->user()->id)
                ->latest()->paginate(10),
            "typeNotes" => TypeNote::all()
            // "etudiants" => User::whereParcourId(optional($this->selectedCours)->parcour_id)
            // ->whereNiveauId(optional($this->selectedCours)->niveau_id)
            // ->get(['id', 'nom', 'prenom'])
        ])
            ->extends('layouts.backend')
            ->section('content');
    }


    public function goToAddNote(Matiere $cours)
    {

        $this->selectedCours = $cours;

        $this->currentPage = PAGECREATEFORM;
    }
}
