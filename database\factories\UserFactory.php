<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $ville = $this->faker->city;
        $pays = $this->faker->country;
        return [
            'nom' => $this->faker->lastName,
            'prenom' => $this->faker->firstName,
            'sexe' => array_rand(["H", "F"]),
            "dateNaissance" => $this->faker->dateTimeBetween("1980-01-01", "2001-12-30"),
            "lieuNaissance"=>  "$pays, $ville",
            "pays"=> $pays,
            "nationalite"=> $this->faker->country,
            "ville"=> $ville,
            "adresse"=> $this->faker->address,
            'email' => $this->faker->unique()->safeEmail(),
            "telephone1"=> $this->faker->phoneNumber,
            "telephone2"=> $this->faker->phoneNumber,
            "noPieceIdentite"=> $this->faker->creditCardNumber,
            'photo' => $this->faker->imageUrl(),
            'matricule' => Str::random(10),
            'parcour_id' => null,
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return static
     */
    public function unverified()
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
