<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Note;
use App\Models\TypeNote;
use App\Models\User;
use Livewire\Component;

class Notes extends Component
{
    public $noteEtus = [];
    public Matiere $current_matiere;

    public $noteType;

    public $isAdded = FALSE;
    public $isEditable = FALSE;

    public $historyNote = FALSE;

    public function mount($coursId)
    {
        $this->current_matiere = Matiere::find($coursId);
        $this->noteType = 1;
        $this->updatedNoteType();
    }

    public function render()
    {
        return view('livewire.enseignant.note.index', [
            "typeNotes" => TypeNote::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        // if ($this->currentPage == PAGEEDITFORM) {

        //     // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
        //     return [
        //         'editUser.nom' => 'required',
        //         'editUser.prenom' => 'required',
        //         'editUser.email' => ['required', 'email', Rule::unique("users", "email")->ignore($this->editUser['id'])],
        //         'editUser.telephone1' => ['required', 'numeric', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])],
        //         'editUser.pieceIdentite' => ['required'],
        //         'editUser.sexe' => 'required',
        //         'editUser.numeroPieceIdentite' => ['required', Rule::unique("users", "pieceIdentite")->ignore($this->editUser['id'])],
        //     ];
        // }

        return [
            "noteEtus.*.note" => "required|numeric|min:1|max:19",
            
        ];
    }


    public function updatedNoteType()
    {
        $note = Note::with(['user', 'historyNote'])->whereMatiereId($this->current_matiere->id)
            ->whereTypeNoteId($this->noteType)->get();

            
        
        if ($note->isEmpty()) {
            $this->isAdded = FALSE;
            $this->noteEtus = [];
            
            foreach (User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($this->current_matiere->ue->annee_universitaire_id)->whereNiveauId($this->current_matiere->ue->niveau_id)->whereParcourId($this->current_matiere->ue->parcour_id))
            ->get(['id', 'nom', 'prenom']) as $user) {
                array_push($this->noteEtus, ["id"=>$user->id, "nom"=> $user->nom, "prenom"=> $user->prenom, "note"=>"", "observation"=>""]);
                
            }
            
            // $this->noteEtus = User::whereParcourId($this->current_matiere->parcour_id)
            //     ->whereNiveauId($this->current_matiere->niveau_id)
            //     ->get(['id', 'nom', 'prenom'])->toArray();

        } else {
            $this->noteEtus = $note->toArray();
            $this->isAdded = TRUE;
            $this->isEditable = $this->noteEtus[0]['history_note']['estModifiable'];
            
        }
    }

    public function addNote()
    {
        $validateArr = [
            "noteEtus.*.note" => "numeric|min:0|max:20",
            
        ];
        $this->validate($validateArr);
        
        
        $historyNote = HistoryNote::create([
            "matiere_id" => $this->current_matiere->id,
            "type_note_id" => $this->noteType,
            "user_id" => auth()->user()->id,
        ]);

        

        foreach ($this->noteEtus as $noteEtu) {
            if ($noteEtu["note"] == "") {
                $note = 0;
            }else {
                $note = $noteEtu["note"];
            }

            Note::create([
                "type_note_id" => $this->noteType,
                "valeur" => $note,
                "user_id" => $noteEtu["id"],
                "matiere_id" => $this->current_matiere->id,
                "history_note_id" => $historyNote->id,
                "observation" => $noteEtu["observation"],
            ]);
        }

        
        

        // if ($this->noteType == 1) {
        //     Matiere::find($this->current_matiere["id"])->update(["p1Added" => 1]);
        // } else if ($this->noteType == 2) {
        //     Matiere::find($this->current_matiere["id"])->update(["p2Added" => 1]);
        // } else if ($this->noteType == 3) {
        //     Matiere::find($this->current_matiere["id"])->update(["examAdded" => 1]);
        // }

        
        $this->updatedNoteType();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note ajouté avec succès!"]);
    }

    public function updateNote(){

    }
}
