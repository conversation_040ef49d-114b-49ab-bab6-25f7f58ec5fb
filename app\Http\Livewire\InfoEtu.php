<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class InfoEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editUser = [];
    public $query;
    public $payments;
    public $filtreNiveau;

    protected $listeners = ["selectDate" => 'getSelectedDate'];

    public function updatingQuery()
    {
        $this->resetPage();
    }

    public function render()
    {
        $persQuery = User::query()->with(['niveau', 'historique', 'info']);

        if ($this->query != "") {
            $persQuery->where(function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('prenom', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreNiveau != "") {
            $persQuery->whereNiveauId($this->filtreNiveau);
        }

        return view('livewire.secretaire.infoetu.index', [
            "etus" => $persQuery->whereRelation('roles', 'role_id', '=', 5)->latest()->paginate(10),
            "niveaux" => Niveau::all(),
            "parcours" => Parcour::all(),
            "annees" => AnneeUniversitaire::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function getSelectedDate($date)
    {
        $this->editUser["date_naissance"] = $date;
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUser.nom' => 'required',
                'editUser.prenom' => '',
                'editUser.email' => '',
                'editUser.sexe' => 'required',
                // 'editUser.matricule' => 'required|unique:users,matricule',
                // 'editUser.matricule' => 'unique:users,matricule',
                // 'editUser.telephone1' => 'required|unique:users,telephone1',
                'editUser.date_naissance' => 'required|before:01/01/14',
                'editUser.lieu_naissance' => '',
                // 'editUser.nationalite' => 'required',
                // 'editUser.ville' => 'required',
                // 'editUser.pays' => 'required',
                'editUser.adresse' => '',
                'editUser.telephone2' => '',
                'editUser.cin' => '',
                'editUser.nom_pere' => '',
                'editUser.nom_mere' => '',
                'editUser.tel_pere' => '',
                'editUser.tel_mere' => '',
                'editUser.nom_tuteur' => '',
                'editUser.tel_tuteur' => '',
                'editUser.date_delivrance' => '',
                'editUser.lieu_delivrance' => '',
                'editUser.duplicata' => '',
                'editUser.parcour_id' => 'required',
                'editUser.niveau_id' => 'required',

                'editUser.telephone1' => '',
                'editUser.matricule' => '',
                // 'editUser.email' => [Rule::unique("users", "email")->ignore($this->editUser['id'])],

            ];
        }

        return [
            'editUser.nom' => 'required',
            'editUser.prenom' => '',
            'editUser.email' => '',
            'editUser.sexe' => 'required',
            'editUser.matricule' => '',
            // 'editUser.matricule' => 'unique:users,matricule',
            'editUser.telephone1' => '',
            'editUser.date_naissance' => 'required|before:01/01/14',
            'editUser.lieu_naissance' => '',
            // 'editUser.nationalite' => 'required',
            // 'editUser.ville' => 'required',
            // 'editUser.pays' => 'required',
            'editUser.adresse' => '',
            'editUser.telephone2' => '',
            'editUser.cin' => '',
            'editUser.nom_pere' => '',
            'editUser.nom_mere' => '',
            'editUser.tel_pere' => '',
            'editUser.tel_mere' => '',
            'editUser.nom_tuteur' => '',
            'editUser.tel_tuteur' => '',
            'editUser.date_delivrance' => '',
            'editUser.lieu_delivrance' => '',
            'editUser.duplicata' => '',
            'editUser.parcour_id' => 'required',
            'editUser.niveau_id' => 'required',
        ];
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        $this->editUser = [];
        // return redirect(request()->header('Referer'));
    }


    public function populatePay()
    {
        $this->payments = HistoriquePayment::with(['user', 'payment'])->whereUserId($this->editUser["id"])->where(function ($query) {
            $query->where('type_payment_id', 1)
                ->orWhere('type_payment_id', 2);
        })->get();
    }

    public function goToCreateUser($id)
    {
        $user = User::find($id);
        $this->editUser = $user->toArray();
        $this->populatePay();
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->currentPage = PAGECREATEFORM;
    }
    public function goToEditUser($id)
    {
        $user = User::find($id);
        $this->editUser = $user->toArray();
        $this->populatePay();
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->currentPage = PAGEEDITFORM;
    }


    public function valider(HistoriquePayment $historique)
    {
        $historique->update([
            "is_valid_sec" => 1,
        ]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Validé avec succès!"]);
        $this->populatePay();
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["editUser"]["photo"] = "media/avatars/avatar0.jpg";

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur

        $user = User::find($this->editUser["id"]);
        $user->update($validationAttributes["editUser"]);
        $user->update([
            "is_filled" => 1,
            "inscription_date" => now()->format('d-m-Y'),
        ]);

        $user->info()->updateOrCreate([
            "niveau_id" => $this->editUser['niveau_id'],
            "parcour_id" => $this->editUser['parcour_id'],
            "annee_universitaire_id" => $this->editUser['annee_universitaire_id'],
        ]);


        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Etudiant mise à jour avec succès!"]);
        // dump($pass);
    }
}
