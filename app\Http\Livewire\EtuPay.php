<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\MoyenPayment;
use App\Models\Niveau;
use App\Models\TypePayment;
use App\Models\User;
use Livewire\Component;

class EtuPay extends Component
{
    public $currentPage = PAGEEDITFORM;

    public $newPay = [];

    public User $current_user;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function mount($userId, $niveauId, $anneeId)
    {
        $this->current_user = User::find($userId);
        $this->current_niveau = Niveau::find($niveauId);
        $this->current_annee = AnneeUniversitaire::find($anneeId);

        $this->populate();
    }
    public function render()
    {
        // $etatPayment = TypePayment::with(['historyPay'])
        // ->withWhereHas('niveau', fn ($q) => $q->whereNiveauId($this->current_niveau->id))->get();
        return view('livewire.caf.payment.index', [
            // "etatPayment" => $etatPayment,
            "moyens" => MoyenPayment::all(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        return [
            'newPay.*.code' => 'required',
            'newPay.*.montant' => 'required',
            'newPay.*.moyen' => 'required',
        ];
    }

    public function populate()
    {
        $mapForCB = function ($value) {
            return $value["id"];
        };

        $typeIds = array_map($mapForCB, TypePayment::whereHas('niveau', fn ($q) => $q->whereNiveauId($this->current_niveau->id))->get()->toArray()); // [1, 2, 4]

        foreach (TypePayment::all() as $type) {
            if (in_array($type->id, $typeIds)) {
                array_push($this->newPay, ["type_id" => $type->id, "type_nom" => $type->nom, "max" => $type->niveau()->whereNiveauId($this->current_niveau->id)->first()->pivot->prix, "code" => "", "montant" => "", "moyen" => ""]);
            }
        }

        // dd($this->newPay);
        // la logique pour charger les roles et les permissions
    }

    public function valider($index){

        $validateArr = [
            "newPay.". $index .".code" => 'required',
            "newPay.". $index .".montant" => "required|numeric|max:". $this->newPay["$index"]["max"],
            "newPay.". $index .".moyen" => 'required',
            
        ];
        $this->validate($validateArr);
        // $validationAttributes = $this->validate();

        $this->current_user->historique()->create([
            "moyen_payment_id" => $this->newPay["$index"]["moyen"],
            "montant" => $this->newPay["$index"]["montant"],
            "code" => $this->newPay["$index"]["code"],
            "type_encaissement_id" => 1,
            "annee_universitaire_id" => $this->current_annee->id,
            "type_payment_id" => $this->newPay["$index"]["type_id"],
        ]);

        // $this->newPay = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Payment effectué avec succès!"]);

        // return redirect(request()->header('Referer'));
    }
}
