<?php

namespace App\Http\Livewire;

use App\Models\Niveau;
use App\Models\Semestre;
use Livewire\Component;

class Semestres extends Component
{
    public $currentPage = PAGELIST;

    public $newSemestre = [];

    public $editSemestre = [];
    
    public function render()
    {
        return view('livewire.deraq.semestre.index', [
            "niveaux" => Niveau::all(),
            "semestres" => Semestre::with('niveau')->get()
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editSemestre.nom' => 'required',
                'editSemestre.niveau_id' => 'required',
            ];
        }

        return [
            'newSemestre.nom' => 'required',
            'newSemestre.niveau_id' => 'required',
            
        ];
    }

    public function goToListSemestre()
    {
        $this->currentPage = PAGELIST;
        $this->newSemestre = [];
    }

    public function goToAddSemestre()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditSemestre(Semestre $semestre){
        $this->editSemestre = $semestre->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addSemestre()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        Semestre::create($validationAttributes["newSemestre"]);

        $this->newSemestre = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Semestre créé avec succès!"]);
        $this->goToListSemestre();
    }

    public function updateSemestre(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        Semestre::find($this->editSemestre["id"])->update($validationAttributes["editSemestre"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Semestre mis à jour avec succès!"]);
        $this->goToListSemestre();
    }

    public function deleteSemestre($id)
    {
        Semestre::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Semestre supprimé avec succès!"]);
    }
}
