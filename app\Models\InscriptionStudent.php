<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InscriptionStudent extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'annee_universitaire_id',
        'parcour_id',
        'niveau_id',
        
    ];

    public function parcours(){
        return $this->belongsTo(Parcour::class, "parcour_id", "id");
    }

    public function niveau(){
        return $this->belongsTo(Niveau::class, "niveau_id", "id");
    }

    public function annee(){
        return $this->belongsTo(AnneeUniversitaire::class, "annee_universitaire_id", "id");
    }

    public function user(){
        return $this->belongsTo(User::class);
    }
}
