<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\MoyenPayment;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\TypePayment;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class PaymentEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;
    public $query;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreAnnee;



    protected $listeners = ["selectDate" => 'getSelectedDate'];

    public function updatingQuery()
    {
        $this->resetPage();
    }
    public function updatingFiltreAnnee(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }

    public function render()
    {
        Carbon::setLocale("fr");

        $persQuery = InscriptionStudent::query()->with(['user', 'parcours', 'niveau', 'annee']);

        if ($this->query != "") {
            $persQuery->whereHas('user', function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('prenom', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreParcours != "") {
            $persQuery->whereParcourId($this->filtreParcours);
        }
        if ($this->filtreNiveau != "") {
            $persQuery->whereNiveauId($this->filtreNiveau);
        }
        if ($this->filtreAnnee != "") {
            $persQuery->whereAnneeUniversitaireId($this->filtreAnnee);
        }

        return view('livewire.caf.payment.index', [
            "etus" => $persQuery->whereRelation('user.roles', 'role_id', '=', 5)
                ->latest()->paginate(10),
            "parcours" => Parcour::all(),
            "annees" => AnneeUniversitaire::all(),
            "niveaux" => Niveau::all(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    // public function populatePay(){
    //     $this->etatPayment = TypePayment::with('historyPay')
    //     ->whereRelation('niveau', 'niveau_id', '=', $this->current_niveau->id)
    //     // ->withWhereHas('historyPay', fn ($q) => $q->whereUserId($this->current_user->id)->whereAnneeUniversitaireId($this->current_annee->id))
    //     ->get();
    // }

    // public function goToEditUser(User $user, Niveau $niveau, AnneeUniversitaire $annee)
    // {
    //     $this->current_user = $user;
    //     $this->current_niveau = $niveau;
    //     $this->current_annee = $annee;
    //     $this->populatePay();
    //     dd($this->etatPayment);
    //     $mapForCB = function ($value) {
    //         return $value["id"];
    //     };

    //     $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
    //     $this->currentPage = PAGEEDITFORM;
         
    // }

    
}
