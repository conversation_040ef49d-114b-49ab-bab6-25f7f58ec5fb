<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\Mention;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\TypePayment;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // User::factory(50)->create();

        // $this->call(EtuL3Seeder::class);
        // $this->call(RoleTableSeeder::class);
        // $this->call(MoyenPaymentTableSeeder::class);
        // $this->call(TypeEncaissementTableSeeder::class);
        // $this->call(TypePaymentTableSeeder::class);


        // $this->call(EtuL2TableSeeder::class);
        // $this->call(PermissionTableSeeder::class);
        // $this->call(AnneeUniversitaireSeeder::class);
        // $this->call(DomaineTableSeeder::class);
        $this->call(MentionTableSeeder::class);
        // $this->call(ParcourTableSeeder::class);
        // $this->call(NiveauTableSeeder::class);
        // $this->call(SemestreTableSeeder::class);
        // $this->call(UeTableSeeder::class);
        
        
        // $this->call(UserTableSeeder::class);
        // $this->call(EtuTableSeeder::class);
        // $this->call(MatiereTableSeeder::class);
        // $this->call(TypeNoteTableSeeder::class);

        // User::find(1)->roles()->attach(1);
        // User::find(2)->roles()->attach(2);

        DB::table("mention_niveau")->delete();

        Mention::find(1)->niveau()->attach(1);
        Mention::find(1)->niveau()->attach(2);
        Mention::find(1)->niveau()->attach(3);
        Mention::find(1)->niveau()->attach(4);
        Mention::find(1)->niveau()->attach(5);

        Mention::find(2)->niveau()->attach(1);
        Mention::find(2)->niveau()->attach(2);
        Mention::find(2)->niveau()->attach(3);
        Mention::find(2)->niveau()->attach(4);
        Mention::find(2)->niveau()->attach(5);

        Mention::find(3)->niveau()->attach(1);
        Mention::find(3)->niveau()->attach(2);
        Mention::find(3)->niveau()->attach(3);
        Mention::find(3)->niveau()->attach(4);
        Mention::find(3)->niveau()->attach(5);

        Mention::find(4)->niveau()->attach(1);
        Mention::find(4)->niveau()->attach(2);
        Mention::find(4)->niveau()->attach(3);
        Mention::find(4)->niveau()->attach(4);
        Mention::find(4)->niveau()->attach(5);

        Mention::find(5)->niveau()->attach(1);
        Mention::find(5)->niveau()->attach(2);
        Mention::find(5)->niveau()->attach(3);
        Mention::find(5)->niveau()->attach(4);
        Mention::find(5)->niveau()->attach(5);

        Mention::find(9)->niveau()->attach(4);
        Mention::find(9)->niveau()->attach(5);

        Mention::find(10)->niveau()->attach(4);
        Mention::find(10)->niveau()->attach(5);

        Mention::find(6)->niveau()->attach(1);
        Mention::find(6)->niveau()->attach(2);
        Mention::find(6)->niveau()->attach(3);
        Mention::find(6)->niveau()->attach(4);
        Mention::find(6)->niveau()->attach(5);

        Mention::find(7)->niveau()->attach(1);
        Mention::find(7)->niveau()->attach(2);
        Mention::find(7)->niveau()->attach(3);

        Mention::find(11)->niveau()->attach(1);
        Mention::find(11)->niveau()->attach(2);
        Mention::find(11)->niveau()->attach(3);

        Mention::find(8)->niveau()->attach(1);
        Mention::find(8)->niveau()->attach(2);
        Mention::find(8)->niveau()->attach(3);
        Mention::find(8)->niveau()->attach(4);
        Mention::find(8)->niveau()->attach(5);

        

        // DB::table("niveau_type_payment")->delete();
        // TypePayment::find(1)->niveau()->attach([1 => ['prix' => 200000]]);
        // TypePayment::find(1)->niveau()->attach([2 => ['prix' => 220000]]);
        // TypePayment::find(1)->niveau()->attach([3 => ['prix' => 220000]]);
        // TypePayment::find(1)->niveau()->attach([4 => ['prix' => 1200000]]);

        // TypePayment::find(2)->niveau()->attach([1 => ['prix' => 5000]]);
        // TypePayment::find(2)->niveau()->attach([2 => ['prix' => 5000]]);
        // TypePayment::find(2)->niveau()->attach([3 => ['prix' => 5000]]);

        // TypePayment::find(3)->niveau()->attach([1 => ['prix' => 20000]]);
        // TypePayment::find(3)->niveau()->attach([2 => ['prix' => 20000]]);
        // TypePayment::find(3)->niveau()->attach([3 => ['prix' => 20000]]);


        // TypePayment::find(11)->niveau()->attach([1 => ['prix' => 75000]]);
        // // TypePayment::find(11)->niveau()->attach(2);
        // // TypePayment::find(11)->niveau()->attach(3);

        // TypePayment::find(12)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(12)->niveau()->attach([2 => ['prix' => 75000]]);
        // // TypePayment::find(12)->niveau()->attach(3);

        // TypePayment::find(13)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(13)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(13)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(14)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(14)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(14)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(15)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(15)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(15)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(16)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(16)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(16)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(17)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(17)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(17)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(18)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(18)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(18)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(19)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(19)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(19)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(20)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(20)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(20)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(21)->niveau()->attach([1 => ['prix' => 75000]]);
        // TypePayment::find(21)->niveau()->attach([2 => ['prix' => 75000]]);
        // TypePayment::find(21)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(22)->niveau()->attach(1);
        // TypePayment::find(22)->niveau()->attach(2);
        // TypePayment::find(22)->niveau()->attach([3 => ['prix' => 80000]]);

        // TypePayment::find(23)->niveau()->attach([1 => ['prix' => 35000]]);
        // TypePayment::find(23)->niveau()->attach([2 => ['prix' => 35000]]);
        // TypePayment::find(23)->niveau()->attach([3 => ['prix' => 35000]]);


        // // // User::find(4)->roles()->attach(4);
        // // // User::find(5)->roles()->attach(5);
        // // // User::find(6)->roles()->attach(6);
    }
}
