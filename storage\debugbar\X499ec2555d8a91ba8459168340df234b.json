{"__meta": {"id": "X499ec2555d8a91ba8459168340df234b", "datetime": "2025-07-01 17:19:43", "utime": 1751379583.273665, "method": "GET", "uri": "/habilitations/download/555/<PERSON><PERSON><PERSON>_<PERSON>_Diplome_Bacc", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379582.353266, "end": 1751379583.27369, "duration": 0.9204239845275879, "duration_str": "920ms", "measures": [{"label": "Booting", "start": 1751379582.353266, "relative_start": 0, "end": 1751379582.934973, "relative_end": 1751379582.934973, "duration": 0.5817070007324219, "duration_str": "582ms", "params": [], "collector": null}, {"label": "Application", "start": 1751379582.935797, "relative_start": 0.5825309753417969, "end": 1751379583.273692, "relative_end": 1.9073486328125e-06, "duration": 0.33789491653442383, "duration_str": "338ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 25633328, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET habilitations/download/{id}/{collection}", "middleware": "web, auth, auth.admin", "controller": "App\\Http\\Controllers\\PdfController@downloadPj", "as": "admin.habilitations.download", "namespace": null, "prefix": "/habilitations", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\app\\Http\\Controllers\\PdfController.php&line=19\">\\app\\Http\\Controllers\\PdfController.php:19-24</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.019180000000000003, "accumulated_duration_str": "19.18ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00631, "duration_str": "6.31ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 32.899}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00329, "duration_str": "3.29ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 32.899, "width_percent": 17.153}, {"sql": "select * from `users` where `users`.`id` = '555' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\PdfController.php", "line": 20}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0016, "duration_str": "1.6ms", "stmt_id": "\\app\\Http\\Controllers\\PdfController.php:20", "connection": "inscriptionimsaa", "start_percent": 50.052, "width_percent": 8.342}, {"sql": "select * from `media` where `media`.`model_id` in (555) and `media`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 23, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 24, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 25, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\PdfController.php", "line": 21}], "duration": 0.007980000000000001, "duration_str": "7.98ms", "stmt_id": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php:545", "connection": "inscriptionimsaa", "start_percent": 58.394, "width_percent": 41.606}]}, "models": {"data": {"Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": 1, "App\\Models\\Role": 1, "App\\Models\\User": 2}, "count": 4}, "livewire": {"data": [], "count": 0}, "gate": {"count": 1, "messages": [{"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-653337965 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653337965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751379583.221245}]}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/habilitations/download/555/Re<PERSON><PERSON>_<PERSON>_Diplome_Bacc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]"}, "request": {"path_info": "/habilitations/download/555/<PERSON><PERSON><PERSON>_<PERSON>_Diplome_Bacc", "status_code": "<pre class=sf-dump id=sf-dump-1681320276 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1681320276\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/pdf", "request_query": "<pre class=sf-dump id=sf-dump-2062428373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2062428373\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-73081813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-73081813\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1339051554 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">identity</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339051554\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-546555212 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56801</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/habilitations/download/555/Releve_ou_Diplome_Bacc</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/habilitations/download/555/Releve_ou_Diplome_Bacc</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"60 characters\">/index.php/habilitations/download/555/Releve_ou_Diplome_Bacc</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"8 characters\">identity</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379582.3533</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379582</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546555212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-360962850 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360962850\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-9005999 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">must-revalidate, post-check=0, pre-check=0, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">89752</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">attachment; filename=&quot;etat_paiement_1747034417.pdf&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:19:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtoN3QvMWoxaTZQTFNrUndSWVpNS3c9PSIsInZhbHVlIjoiMDkxSENaSlZVdE9EeFV6ajVOQlhFcEVhREN1OXFFNTdWZk5kZ2lUOEFlai9qblZ5L0w1R291R0k2OVNPRDZ0bHByUHprdkZOMXIybkJ5MGMxeTVTVTJiS0UvK09hNHFZK3RhVi8zTjV3RFV2WkNzSGx5aUN0Q1lhbTRreUJEQjEiLCJtYWMiOiI0YjQyNGY3ZWY5ZDk3Y2E3NWNkY2RlYjE3ZGZmMzc2NjJlOTU5YTYyYWQ2ZjViYjQyNTU1YTQ1Yjk5YjUwZjI1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImdKT2xXY3M2NmRPaVc3cWV3anpxS1E9PSIsInZhbHVlIjoiUS9sL3htNWE5T3ZTei81NENJdUZpQjllM2c0VjN2aW1jcXJiUEl1RjhtU21RVGFwQlVmb3NhQXpOeEZzNUk5Ty9ESU05Q3plUVU0VzN2eWVsOVgyTVBEbW5xd21OQ0JiNXQwWEIzZyt1ZnQrOU95eXQ3STBEd2xueFJyQ3dLOXQiLCJtYWMiOiIxZjUzMDFlNjg3NTFkMDMwMGRhNTEzODI5NzgxZjNhNTg0MDkxOGM0YWM0OTY2ZTEzNzY5MjVkYWE5Njc0NzFlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtoN3QvMWoxaTZQTFNrUndSWVpNS3c9PSIsInZhbHVlIjoiMDkxSENaSlZVdE9EeFV6ajVOQlhFcEVhREN1OXFFNTdWZk5kZ2lUOEFlai9qblZ5L0w1R291R0k2OVNPRDZ0bHByUHprdkZOMXIybkJ5MGMxeTVTVTJiS0UvK09hNHFZK3RhVi8zTjV3RFV2WkNzSGx5aUN0Q1lhbTRreUJEQjEiLCJtYWMiOiI0YjQyNGY3ZWY5ZDk3Y2E3NWNkY2RlYjE3ZGZmMzc2NjJlOTU5YTYyYWQ2ZjViYjQyNTU1YTQ1Yjk5YjUwZjI1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImdKT2xXY3M2NmRPaVc3cWV3anpxS1E9PSIsInZhbHVlIjoiUS9sL3htNWE5T3ZTei81NENJdUZpQjllM2c0VjN2aW1jcXJiUEl1RjhtU21RVGFwQlVmb3NhQXpOeEZzNUk5Ty9ESU05Q3plUVU0VzN2eWVsOVgyTVBEbW5xd21OQ0JiNXQwWEIzZyt1ZnQrOU95eXQ3STBEd2xueFJyQ3dLOXQiLCJtYWMiOiIxZjUzMDFlNjg3NTFkMDMwMGRhNTEzODI5NzgxZjNhNTg0MDkxOGM0YWM0OTY2ZTEzNzY5MjVkYWE5Njc0NzFlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9005999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659866710 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/habilitations/download/555/Releve_ou_Diplome_Bacc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659866710\", {\"maxDepth\":0})</script>\n"}}