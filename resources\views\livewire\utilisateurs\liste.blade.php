 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">
         <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
             <div class="flex-grow-1">
                 <h1 class="h3 fw-bold mb-2">
                     <i class="fa fa-users me-2 text-primary"></i>
                     Gestion des Utilisateurs
                 </h1>
                 <p class="fs-sm fw-medium text-muted mb-0">
                     Gérez les comptes utilisateurs, rôles et permissions
                 </p>
             </div>
             <div class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3">
                 <div class="d-flex gap-2">
                     <button type="button" class="btn btn-sm btn-outline-primary" wire:click="exportUsers">
                         <i class="fa fa-download me-1"></i> Exporter
                     </button>
                     <button type="button" class="btn btn-sm btn-primary" wire:click="goToAddUser()">
                         <i class="fa fa-user-plus me-1"></i> Nouvel Utilisateur
                     </button>
                 </div>
             </div>
         </div>
     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">
     <!-- Filtres et statistiques -->
     <div class="row mb-4">
         <div class="col-lg-3 col-md-6">
             <div class="block block-rounded text-center">
                 <div class="block-content block-content-full">
                     <div class="fs-2 fw-bold text-primary">{{ $totalUsers }}</div>
                     <div class="fs-sm fw-medium text-muted text-uppercase">Total Utilisateurs</div>
                 </div>
             </div>
         </div>
         <div class="col-lg-3 col-md-6">
             <div class="block block-rounded text-center">
                 <div class="block-content block-content-full">
                     <div class="fs-2 fw-bold text-success">{{ $activeUsers }}</div>
                     <div class="fs-sm fw-medium text-muted text-uppercase">Actifs</div>
                 </div>
             </div>
         </div>
         <div class="col-lg-3 col-md-6">
             <div class="block block-rounded text-center">
                 <div class="block-content block-content-full">
                     <div class="fs-2 fw-bold text-info">{{ $studentsCount }}</div>
                     <div class="fs-sm fw-medium text-muted text-uppercase">Étudiants</div>
                 </div>
             </div>
         </div>
         <div class="col-lg-3 col-md-6">
             <div class="block block-rounded text-center">
                 <div class="block-content block-content-full">
                     <div class="fs-2 fw-bold text-warning">{{ $staffCount }}</div>
                     <div class="fs-sm fw-medium text-muted text-uppercase">Personnel</div>
                 </div>
             </div>
         </div>
     </div>

     <!-- Filtres avancés -->
     <div class="block block-rounded mb-4">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 <i class="fa fa-filter me-2"></i>Filtres
             </h3>
             <div class="block-options">
                 <button type="button" class="btn-block-option d-none d-md-block" data-toggle="block-option" data-action="content_toggle">
                     <i class="si si-arrow-up"></i>
                 </button>
                 <!-- Mobile filter toggle -->
                 <button type="button" class="btn btn-sm btn-outline-primary d-md-none" onclick="toggleMobileFilters()">
                     <i class="fa fa-filter me-1"></i> Filtres
                 </button>
             </div>
         </div>
         <div class="block-content" id="filtersContent">
             <!-- Mobile-first search bar -->
             <div class="d-md-none mb-3">
                 <div class="input-group">
                     <input type="search" wire:model.debounce.300ms="query" class="form-control"
                            placeholder="Rechercher un utilisateur...">
                     <span class="input-group-text">
                         <i class="fa fa-search"></i>
                     </span>
                 </div>
             </div>

             <!-- Collapsible filters for mobile -->
             <div class="collapse d-md-block" id="mobileFilters">
                 <div class="row g-3">
                     <div class="col-md-4 d-none d-md-block">
                         <label class="form-label">Recherche</label>
                         <div class="input-group">
                             <input type="search" wire:model.debounce.300ms="query" class="form-control"
                                    placeholder="Nom, prénom, email...">
                             <span class="input-group-text">
                                 <i class="fa fa-search"></i>
                             </span>
                         </div>
                     </div>
                     <div class="col-md-2 col-6">
                         <label class="form-label">Rôle</label>
                         <select wire:model="filterRole" class="form-select">
                             <option value="">Tous les rôles</option>
                             @foreach($roles as $role)
                                 <option value="{{ $role->id }}">{{ $role->nom }}</option>
                             @endforeach
                         </select>
                     </div>
                     <div class="col-md-2 col-6">
                         <label class="form-label">Statut</label>
                         <select wire:model="filterStatus" class="form-select">
                             <option value="">Tous</option>
                             <option value="active">Actif</option>
                             <option value="inactive">Inactif</option>
                         </select>
                     </div>
                     <div class="col-md-2 col-6">
                         <label class="form-label">Par page</label>
                         <select wire:model="perPage" class="form-select">
                             <option value="5">5</option>
                             <option value="10">10</option>
                             <option value="15">15</option>
                             <option value="25">25</option>
                             <option value="50">50</option>
                         </select>
                     </div>
                     <div class="col-md-2 col-6">
                         <label class="form-label d-none d-md-block">&nbsp;</label>
                         <div class="d-grid">
                             <button type="button" wire:click="resetFilters" class="btn btn-outline-secondary">
                                 <i class="fa fa-undo me-1"></i>
                                 <span class="d-none d-md-inline">Reset</span>
                                 <span class="d-md-none">Effacer</span>
                             </button>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div>

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 <i class="fa fa-list me-2"></i>Liste des Utilisateurs
                 <span class="badge bg-primary ms-2">{{ $users->total() }}</span>
             </h3>
             <div class="block-options">
                 <div class="dropdown">
                     <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                             data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                         <i class="fa fa-cog me-1"></i> Actions
                     </button>
                     <div class="dropdown-menu dropdown-menu-end">
                         <a class="dropdown-item" href="#" wire:click.prevent="selectAll">
                             <i class="fa fa-check-square me-2"></i> Tout sélectionner
                         </a>
                         <a class="dropdown-item" href="#" wire:click.prevent="deselectAll">
                             <i class="fa fa-square me-2"></i> Tout désélectionner
                         </a>
                         <div class="dropdown-divider"></div>
                         <a class="dropdown-item text-danger" href="#" wire:click.prevent="deleteSelected">
                             <i class="fa fa-trash me-2"></i> Supprimer sélectionnés
                         </a>
                     </div>
                 </div>
             </div>
         </div>
         <div class="block-content block-content-full">
             @if($selectedUsers && count($selectedUsers) > 0)
                 <div class="alert alert-info d-flex align-items-center" role="alert">
                     <i class="fa fa-info-circle me-2"></i>
                     <div>
                         {{ count($selectedUsers) }} utilisateur(s) sélectionné(s)
                         <button type="button" class="btn btn-sm btn-outline-danger ms-3" wire:click="deleteSelected">
                             <i class="fa fa-trash me-1"></i> Supprimer
                         </button>
                     </div>
                 </div>
             @endif

             <!-- Loading State -->
             <div wire:loading.delay class="text-center py-4">
                 <div class="spinner-border text-primary" role="status">
                     <span class="visually-hidden">Chargement...</span>
                 </div>
                 <div class="mt-2 text-muted">Chargement des données...</div>
             </div>

             <!-- Mobile Cards (visible on small screens) -->
             <div class="d-block d-md-none" wire:loading.remove>
                 @forelse ($users as $user)
                     <div class="card mb-3 {{ in_array($user->id, $selectedUsers ?? []) ? 'border-primary' : '' }}">
                         <div class="card-body">
                             <div class="d-flex align-items-start">
                                 <div class="form-check me-3">
                                     <input class="form-check-input" type="checkbox"
                                            wire:model="selectedUsers" value="{{ $user->id }}"
                                            id="mobile_user{{ $user->id }}">
                                 </div>
                                 <div class="flex-shrink-0 me-3">
                                     <img class="img-avatar img-avatar48"
                                          src="{{ $user->photo ? asset($user->photo) : asset('media/avatars/avatar0.jpg') }}"
                                          alt="Avatar">
                                 </div>
                                 <div class="flex-grow-1">
                                     <h6 class="card-title mb-1">{{ $user->nom }} {{ $user->prenom }}</h6>
                                     <p class="card-text text-muted mb-2">
                                         <i class="fa fa-envelope me-1"></i>{{ $user->email }}<br>
                                         <i class="fa fa-phone me-1"></i>{{ $user->telephone1 }}
                                     </p>
                                     <div class="mb-2">
                                         @if($user->roles->count() > 0)
                                             @foreach($user->roles as $role)
                                                 <span class="badge bg-primary-subtle text-primary me-1">{{ $role->nom }}</span>
                                             @endforeach
                                         @else
                                             <span class="text-muted">Aucun rôle</span>
                                         @endif
                                     </div>
                                     <div class="d-flex justify-content-between align-items-center">
                                         <small class="text-muted">
                                             @if($user->email && $user->password)
                                                 <span class="badge bg-success">Actif</span>
                                             @else
                                                 <span class="badge bg-warning">Incomplet</span>
                                             @endif
                                         </small>
                                         <div class="btn-group" role="group">
                                             <button type="button" class="btn btn-sm btn-outline-info"
                                                     title="Voir" wire:click="viewUser({{ $user->id }})">
                                                 <i class="fa fa-eye"></i>
                                             </button>
                                             <button type="button" class="btn btn-sm btn-outline-primary"
                                                     title="Modifier" wire:click="goToEditUser({{ $user->id }})">
                                                 <i class="fa fa-edit"></i>
                                             </button>
                                             <button type="button" class="btn btn-sm btn-outline-danger"
                                                     title="Supprimer"
                                                     wire:click="confirmDelete('{{ $user->nom }} {{ $user->prenom }}', {{ $user->id }})">
                                                 <i class="fa fa-trash"></i>
                                             </button>
                                         </div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 @empty
                     <div class="text-center py-5">
                         <i class="fa fa-users fa-3x mb-3 text-muted"></i>
                         <h5>Aucun utilisateur trouvé</h5>
                         <p class="text-muted">Aucun utilisateur ne correspond à vos critères de recherche.</p>
                         @if($query || $filterRole || $filterStatus)
                             <button type="button" wire:click="resetFilters" class="btn btn-outline-primary">
                                 <i class="fa fa-undo me-1"></i> Réinitialiser les filtres
                             </button>
                         @else
                             <button type="button" wire:click="goToAddUser()" class="btn btn-primary">
                                 <i class="fa fa-user-plus me-1"></i> Ajouter le premier utilisateur
                             </button>
                         @endif
                     </div>
                 @endforelse
             </div>

             <!-- Desktop Table (hidden on small screens) -->
             <div class="table-responsive d-none d-md-block" wire:loading.remove>
                 <table class="table table-borderless table-striped table-vcenter">
                     <thead class="bg-body-light">
                         <tr>
                             <th class="text-center" style="width: 40px;">
                                 <div class="form-check">
                                     <input class="form-check-input" type="checkbox" wire:model="selectAll" id="selectAllUsers">
                                     <label class="form-check-label" for="selectAllUsers"></label>
                                 </div>
                             </th>
                             <th style="width: 60px;">
                                 <a href="#" wire:click.prevent="sortBy('id')" class="text-decoration-none">
                                     ID
                                     @if($sortField === 'id')
                                         <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                     @else
                                         <i class="fa fa-sort ms-1 text-muted"></i>
                                     @endif
                                 </a>
                             </th>
                             <th>
                                 <a href="#" wire:click.prevent="sortBy('nom')" class="text-decoration-none">
                                     Nom et Prénom
                                     @if($sortField === 'nom')
                                         <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                     @else
                                         <i class="fa fa-sort ms-1 text-muted"></i>
                                     @endif
                                 </a>
                             </th>
                             <th class="d-none d-md-table-cell">
                                 <a href="#" wire:click.prevent="sortBy('email')" class="text-decoration-none">
                                     Email
                                     @if($sortField === 'email')
                                         <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                     @else
                                         <i class="fa fa-sort ms-1 text-muted"></i>
                                     @endif
                                 </a>
                             </th>
                             <th class="d-none d-lg-table-cell">Rôle(s)</th>
                             <th class="d-none d-lg-table-cell">
                                 <a href="#" wire:click.prevent="sortBy('created_at')" class="text-decoration-none">
                                     Créé le
                                     @if($sortField === 'created_at')
                                         <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                     @else
                                         <i class="fa fa-sort ms-1 text-muted"></i>
                                     @endif
                                 </a>
                             </th>
                             <th class="d-none d-sm-table-cell">Statut</th>
                             <th class="text-center" style="width: 120px;">Actions</th>
                         </tr>
                     </thead>
                     <tbody>
                         @forelse ($users as $user)
                             <tr class="{{ in_array($user->id, $selectedUsers ?? []) ? 'table-active' : '' }}">
                                 <td class="text-center">
                                     <div class="form-check">
                                         <input class="form-check-input" type="checkbox"
                                                wire:model="selectedUsers" value="{{ $user->id }}"
                                                id="user{{ $user->id }}">
                                         <label class="form-check-label" for="user{{ $user->id }}"></label>
                                     </div>
                                 </td>
                                 <td class="text-center text-muted">{{ $user->id }}</td>
                                 <td>
                                     <div class="d-flex align-items-center">
                                         <div class="flex-shrink-0 me-3">
                                             <img class="img-avatar img-avatar32"
                                                  src="{{ $user->photo ? asset($user->photo) : asset('media/avatars/avatar0.jpg') }}"
                                                  alt="Avatar">
                                         </div>
                                         <div class="flex-grow-1">
                                             <div class="fw-semibold">{{ $user->nom }} {{ $user->prenom }}</div>
                                             <div class="fs-sm text-muted d-md-none">{{ $user->email }}</div>
                                         </div>
                                     </div>
                                 </td>
                                 <td class="d-none d-md-table-cell">
                                     <div class="text-muted">{{ $user->email }}</div>
                                     <div class="fs-sm text-muted">{{ $user->telephone1 }}</div>
                                 </td>
                                 <td class="d-none d-lg-table-cell">
                                     @if($user->roles->count() > 0)
                                         @foreach($user->roles as $role)
                                             <span class="badge bg-primary-subtle text-primary me-1">{{ $role->nom }}</span>
                                         @endforeach
                                     @else
                                         <span class="text-muted">Aucun rôle</span>
                                     @endif
                                 </td>
                                 <td class="d-none d-lg-table-cell">
                                     <div class="text-muted">{{ $user->created_at->format('d/m/Y') }}</div>
                                     <div class="fs-sm text-muted">{{ $user->created_at->diffForHumans() }}</div>
                                 </td>
                                 <td class="d-none d-sm-table-cell">
                                     @if($user->email && $user->password)
                                         <span class="badge bg-success">Actif</span>
                                     @else
                                         <span class="badge bg-warning">Incomplet</span>
                                     @endif
                                 </td>
                                 <td class="text-center">
                                     <div class="btn-group" role="group">
                                         <button type="button" class="btn btn-sm btn-outline-info"
                                                 title="Voir le profil" wire:click="viewUser({{ $user->id }})">
                                             <i class="fa fa-eye"></i>
                                         </button>
                                         <button type="button" class="btn btn-sm btn-outline-primary"
                                                 title="Modifier" wire:click="goToEditUser({{ $user->id }})">
                                             <i class="fa fa-edit"></i>
                                         </button>
                                         <button type="button" class="btn btn-sm btn-outline-danger"
                                                 title="Supprimer"
                                                 wire:click="confirmDelete('{{ $user->nom }} {{ $user->prenom }}', {{ $user->id }})">
                                             <i class="fa fa-trash"></i>
                                         </button>
                                     </div>
                                 </td>
                             </tr>
                         @empty
                             <tr>
                                 <td colspan="8" class="text-center py-5">
                                     <div class="text-muted">
                                         <i class="fa fa-users fa-3x mb-3"></i>
                                         <div class="h5">Aucun utilisateur trouvé</div>
                                         <p>Aucun utilisateur ne correspond à vos critères de recherche.</p>
                                         @if($query || $filterRole || $filterStatus)
                                             <button type="button" wire:click="resetFilters" class="btn btn-outline-primary">
                                                 <i class="fa fa-undo me-1"></i> Réinitialiser les filtres
                                             </button>
                                         @else
                                             <button type="button" wire:click="goToAddUser()" class="btn btn-primary">
                                                 <i class="fa fa-user-plus me-1"></i> Ajouter le premier utilisateur
                                             </button>
                                         @endif
                                     </div>
                                 </td>
                             </tr>
                         @endforelse
                     </tbody>
                 </table>
             </div>

             <!-- Pagination améliorée -->
             @if($users->hasPages())
                 <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center mt-4">
                     <div class="mb-2 mb-sm-0">
                         <span class="text-muted">
                             Affichage de {{ $users->firstItem() }} à {{ $users->lastItem() }}
                             sur {{ $users->total() }} résultats
                         </span>
                     </div>
                     <nav aria-label="Navigation des utilisateurs">
                         {{ $users->links() }}
                     </nav>
                 </div>
             @endif
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->

<script>
    function toggleMobileFilters() {
        const mobileFilters = document.getElementById('mobileFilters');
        const bsCollapse = new bootstrap.Collapse(mobileFilters, {
            toggle: true
        });
    }

    // Auto-hide mobile filters when screen size changes
    window.addEventListener('resize', function() {
        const mobileFilters = document.getElementById('mobileFilters');
        if (window.innerWidth >= 768) {
            mobileFilters.classList.add('show');
        }
    });

    // Initialize mobile filters state
    document.addEventListener('DOMContentLoaded', function() {
        const mobileFilters = document.getElementById('mobileFilters');
        if (window.innerWidth >= 768) {
            mobileFilters.classList.add('show');
        }
    });
</script>
