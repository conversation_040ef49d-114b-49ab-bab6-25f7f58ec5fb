<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Semestre extends Model
{
    use HasFactory, SoftDeletes;

    public $timestamps = false;

    protected $fillable = [
        'nom',
        'niveau_id',
        
    ];

    public function ue(){
        return $this->hasMany(Ue::class);
    }

    public function niveau(){
        return $this->belongsTo(Niveau::class);
    }

    public function matiere(){
        return $this->hasManyThrough(Matiere::class, Ue::class);
    }
}
