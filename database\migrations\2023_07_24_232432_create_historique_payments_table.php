<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('historique_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId("user_id")->constrained();
            $table->foreignId("type_payment_id")->constrained();
            $table->foreignId("moyen_payment_id")->constrained();
            $table->foreignId("type_encaissement_id")->constrained();
            $table->foreignId("annee_universitaire_id")->constrained();
            $table->integer('montant');
            $table->string('code')->unique();
            $table->boolean("is_valid")->default(0);
            $table->boolean("is_valid_sec")->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('historique_payments');
    }
};
