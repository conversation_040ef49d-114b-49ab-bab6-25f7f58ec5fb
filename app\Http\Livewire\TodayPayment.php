<?php

namespace App\Http\Livewire;

use App\Models\HistoriquePayment;
use App\Models\MoyenPayment;
use App\Models\TypePayment;
use App\Models\User;
use Illuminate\Validation\Rule;
use Livewire\Component;

class TodayPayment extends Component
{
    public $currentPage = PAGELIST;

    public $editPay = [];

    public User $current_user;

    public function render()
    {
        return view('livewire.administration.todaypayment.index', [
            "pays" => HistoriquePayment::with(['user', 'payment', 'moyen', 'encaissement'])->whereDate('created_at', now()->today())->get(),
            "moyens" => MoyenPayment::all(),
            "types" => TypePayment::all(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {
            return [
                'editPay.moyen_payment_id' => 'required',
                'editPay.type_payment_id' => 'required',
                'editPay.montant' => 'required',
                'editPay.code' => ['required', Rule::unique("historique_payments", "code")->ignore($this->editPay['id'])],
            ];
        }
    }

    public function valider(HistoriquePayment $historique)
    {
        $historique->update([
            "is_valid" => 1,
        ]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Validé avec succès!"]);
        $this->render();
    }

    public function goToEditPay($id)
    {
        $this->editPay = HistoriquePayment::find($id)->toArray();
        $this->current_user = User::find($this->editPay["user_id"]);
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
    }

    public function goToListPay()
    {
        $this->currentPage = PAGELIST;
        $this->editPay = [];
    }

    public function updatePay()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        HistoriquePayment::find($this->editPay["id"])->update($validationAttributes["editPay"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Historique de payment mis à jour avec succès!"]);

    }
}
