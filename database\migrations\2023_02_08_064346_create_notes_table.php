<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notes', function (Blueprint $table) {
            $table->id();
            $table->double('valeur', 15, 2);
            $table->string("observation")->nullable();
            $table->foreignId("type_note_id")->constrained();
            $table->foreignId("user_id")->constrained();
            $table->foreignId("matiere_id")->constrained();
            $table->foreignId("history_note_id")->constrained();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('notes', function (Blueprint $table) {
            $table->dropForeign("user_id");
            $table->dropForeign("matiere_id");
            $table->dropForeign("type_note_id");
            $table->dropForeign("history_note_id");
        });
        Schema::dropIfExists('notes');
    }
};
