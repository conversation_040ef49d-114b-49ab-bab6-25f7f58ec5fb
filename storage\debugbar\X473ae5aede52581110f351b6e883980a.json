{"__meta": {"id": "X473ae5aede52581110f351b6e883980a", "datetime": "2025-07-01 17:19:03", "utime": 1751379543.298735, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379542.651426, "end": 1751379543.298779, "duration": 0.647352933883667, "duration_str": "647ms", "measures": [{"label": "Booting", "start": 1751379542.651426, "relative_start": 0, "end": 1751379543.179309, "relative_end": 1751379543.179309, "duration": 0.5278828144073486, "duration_str": "528ms", "params": [], "collector": null}, {"label": "Application", "start": 1751379543.180033, "relative_start": 0.5286068916320801, "end": 1751379543.298781, "relative_end": 1.9073486328125e-06, "duration": 0.11874794960021973, "duration_str": "119ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23286224, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yUyN1I5WFb7gw91U7QRWMfQWwOetPdrPGe5CtG0B", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1335789824 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1335789824\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1969896177 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1969896177\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1509617963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1509617963\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-560004502 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560004502\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-246458508 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56753</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379542.6514</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379542</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246458508\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1351904161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1351904161\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-553811796 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:19:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNoWENudGlBOWg3YU9jWmlpSUpSTnc9PSIsInZhbHVlIjoieCtuRFB2dnRzY1l0Qnc1eU52cXI2V2NxT2hybnlrL2EvemFzM0pKWHAyK0tvOVBrY2k0bG5qeVJlY0ZQTFVFcVJqUGZlSElxZmoxKzVUL3hIME9CUXVJUDFab0QrSkJ1amNrajJ1Q01RLzF5ZXl1UTRRRkFkR0hWVVNWdHp6N2YiLCJtYWMiOiIzNzZjNmI3NDk4MjEzYTNkODliNzE3Zjc0MzlkZmUxYmM4ZGU0MDAzZTdhNWZjZTBjMDkyNWU5MWY5ODMyN2M3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6Im1wVFl3OFRpK2V5UjNDRFVUT3BoOVE9PSIsInZhbHVlIjoiMVVRelNuMndWZkdZZHRZTXRHLzArTDFoUEhUNzlhUDl4RHlSSlYyQ0FmSmxkenNDWmQvejJ3OE9RaFV3bWpwdzFDVi9aUzJrK0I4MHlJL0dheWRpU0NOdW5LdFk3MnRETEFWb0xRV2V0Ly80RkdKS1VPdTNyRlBSN2xzRm5SUWEiLCJtYWMiOiJkZjY3OGUyMDhhOTIzYjc0N2IzMDgyZGQ1M2FmYjJjMmE0MDhhOTc1NDY1ODVjYmMwN2M1NDA3YzQ3NjFjMzA3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNoWENudGlBOWg3YU9jWmlpSUpSTnc9PSIsInZhbHVlIjoieCtuRFB2dnRzY1l0Qnc1eU52cXI2V2NxT2hybnlrL2EvemFzM0pKWHAyK0tvOVBrY2k0bG5qeVJlY0ZQTFVFcVJqUGZlSElxZmoxKzVUL3hIME9CUXVJUDFab0QrSkJ1amNrajJ1Q01RLzF5ZXl1UTRRRkFkR0hWVVNWdHp6N2YiLCJtYWMiOiIzNzZjNmI3NDk4MjEzYTNkODliNzE3Zjc0MzlkZmUxYmM4ZGU0MDAzZTdhNWZjZTBjMDkyNWU5MWY5ODMyN2M3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6Im1wVFl3OFRpK2V5UjNDRFVUT3BoOVE9PSIsInZhbHVlIjoiMVVRelNuMndWZkdZZHRZTXRHLzArTDFoUEhUNzlhUDl4RHlSSlYyQ0FmSmxkenNDWmQvejJ3OE9RaFV3bWpwdzFDVi9aUzJrK0I4MHlJL0dheWRpU0NOdW5LdFk3MnRETEFWb0xRV2V0Ly80RkdKS1VPdTNyRlBSN2xzRm5SUWEiLCJtYWMiOiJkZjY3OGUyMDhhOTIzYjc0N2IzMDgyZGQ1M2FmYjJjMmE0MDhhOTc1NDY1ODVjYmMwN2M1NDA3YzQ3NjFjMzA3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553811796\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1376476509 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yUyN1I5WFb7gw91U7QRWMfQWwOetPdrPGe5CtG0B</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376476509\", {\"maxDepth\":0})</script>\n"}}