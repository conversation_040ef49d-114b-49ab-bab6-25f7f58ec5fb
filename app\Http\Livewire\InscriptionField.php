<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\InscriptionStudent;
use App\Models\MoyenPayment;
use App\Models\Niveau;
use App\Models\User;
use Livewire\Component;

class InscriptionField extends Component
{
    public $currentPage = PAGECREATEFORM;

    public $newUser = [];

    public function render()
    {
        return view('livewire.caf.inscription.index', [
            "niveaux" => Niveau::all(),
            "moyens" => MoyenPayment::all(),
            "annees" => AnneeUniversitaire::all(),
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        return [
            'newUser.nom' => 'required',
            'newUser.prenom' => '',
            'newUser.niveau_id' => 'required',
            'newUser.moyen' => 'required',
            'newUser.droit' => 'numeric|min:0|required',
            'newUser.fiche' => 'numeric|min:0|max:5000|required',
            // 'newUser.exam' => 'numeric|min:0|max:20000',
            // 'newUser.polo' => 'numeric|min:0|max:30000',
            'newUser.droitcode' => 'required',
            'newUser.fichecode' => 'required',
            'newUser.annee' => 'required',
            // 'newUser.examcode' => '',
            // 'newUser.polocode' => '',
        ];
    }

    public function addUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        $user = User::create([
            "nom" => $validationAttributes["newUser"]["nom"],
            "prenom" => $validationAttributes["newUser"]["prenom"],
            "niveau_id" => $validationAttributes["newUser"]["niveau_id"],
        ]);

        $user->roles()->attach(5);

        $user->info()->create([
            "annee_universitaire_id" => $validationAttributes["newUser"]["annee"],
            "niveau_id" => $validationAttributes["newUser"]["niveau_id"],
        ]);

        $user->historique()->create([
            "moyen_payment_id" => $validationAttributes["newUser"]["moyen"],
            "montant" => $validationAttributes["newUser"]["droit"],
            "code" => $validationAttributes["newUser"]["droitcode"],
            "type_encaissement_id" => 1,
            "annee_universitaire_id" => $validationAttributes["newUser"]["annee"],
            "type_payment_id" => 1,
        ]);

        $user->historique()->create([
            "moyen_payment_id" => $validationAttributes["newUser"]["moyen"],
            "montant" => $validationAttributes["newUser"]["fiche"],
            "code" => $validationAttributes["newUser"]["fichecode"],
            "type_encaissement_id" => 1,
            "annee_universitaire_id" => $validationAttributes["newUser"]["annee"],
            "type_payment_id" => 2,
        ]);

        // $user->payment()->create([
        //     "moyen_payment_id" => $validationAttributes["newUser"]["moyen"],
        //     "montant" => $validationAttributes["newUser"]["exam"],
        //     "code" => $validationAttributes["newUser"]["examcode"],
        //     "type_encaissement_id" => 1,
        //     "type_payment_id" => 3,
        // ]);

        // $user->payment()->create([
        //     "moyen_payment_id" => $validationAttributes["newUser"]["moyen"],
        //     "montant" => $validationAttributes["newUser"]["polo"],
        //     "code" => $validationAttributes["newUser"]["polocode"],
        //     "type_encaissement_id" => 1,
        //     "type_payment_id" => 25,
        // ]);

        $this->newUser = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur créé avec succès!"]);
        // dump($pass);
    }
}
