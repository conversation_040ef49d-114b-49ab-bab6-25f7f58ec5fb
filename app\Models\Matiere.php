<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Matiere extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nom',
        'syllabus',
        'user_id',
        'ue_id',
        'parcour_id',
        'niveau_id',
        'code',
        
    ];

    public function user(){
        return $this->belongsTo(User::class, "user_id", "id");
    }
    
    public function ue(){
        return $this->belongsTo(Ue::class);
    }

    public function history(){
        return $this->hasMany(HistoryNote::class);
    }

    public function note(){
        return $this->hasMany(Note::class);
    }

    public function notesP1(){
        return $this->hasMany(Note::class)->whereTypeNoteId(1);
    }

    public function notesP2(){
        return $this->hasMany(Note::class)->whereTypeNoteId(2);
    }

    public function notesExam(){
        return $this->hasMany(Note::class)->whereTypeNoteId(3);
    }

    public function getMoyenneAttribute()
    {
        $sumP1 = 0;
        $sumP2 = 0;
        $sumEx = 0;
        $count = 0;
        
        foreach ($this->notesP1 as $note) {
            $sumP1 = $note->valeur;
            $count++;
        }

        foreach ($this->notesP2 as $note) {
            $sumP2 = $note->valeur;
            $count++;
        }

        foreach ($this->notesExam as $note) {
            $sumEx = $note->valeur;
            $count++;
        }
        
        return $count > 0 ? ($sumP1+$sumP2+($sumEx*2))/4 : 0;
    }
}
