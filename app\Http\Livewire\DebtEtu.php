<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\TypePayment;
use App\Models\User;
use Livewire\Component;

class DebtEtu extends Component
{
    public $currentPage = PAGELIST;

    public $debtPay = [];
    public $newEtus = [];

    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.administration.debtpay.index', [
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required',
            'newEtus.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->debtPay = [];
    }

    public function goToEtat()
    {
        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["newEtus"]["niveau_id"], $validationAttributes["newEtus"]["annee_universitaire_id"]);

        $this->currentPage = PAGECREATEFORM;
    }

    public function generate($niveau_id, $annee_universitaire_id)
    {
        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);

        foreach (TypePayment::whereHas('niveau', fn ($q) => $q->whereNiveauId($niveau_id))->get() as $type) {
            $type_prix = $type->niveau()->whereNiveauId($this->current_niveau->id)->first()->pivot->prix;


            foreach (User::withWhereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))->get() as $user) {
                $prix = 0;
                // dd($user);
                if ($user->historique->where('type_payment_id', $type->id)->where('annee_universitaire_id', $annee_universitaire_id)->isNotEmpty()) {
                    foreach ($user->historique->where('type_payment_id', $type->id)->where('annee_universitaire_id', $annee_universitaire_id) as $histo) {
                        $prix += $histo->montant;
                    }

                    $montant = number_format($prix, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                    $manque = number_format($type_prix - $prix, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');

                    if ($prix < $type_prix) {
                        array_push($this->debtPay, ["user_nom" => $user->nom, "user_prenom" => $user->prenom, "user_parcours" => $user->info['0']->parcours->sigle, "type_nom" => $type->nom, "montant" => $montant, "manque" => $manque]);
                    }
                }
            }
        }
        // dd($this->debtPay);
    }
}
