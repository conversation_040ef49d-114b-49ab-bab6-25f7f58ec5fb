<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Gate::define("admin", function(User $user){
            return $user->hasRole("admin");
        });

        Gate::define("enseignant", function(User $user){
            return $user->hasRole("enseignant");
        });

        Gate::define("deraq", function(User $user){
            return $user->hasRole("deraq");
        });

        Gate::define("secretaire", function(User $user){
            return $user->hasRole("secretaire");
        });

        Gate::define("caf", function(User $user){
            return $user->hasRole("caf");
        });

        Gate::define("etudiant", function(User $user){
            return $user->hasRole("etudiant");
        });

        Gate::before(function (User $user) {
           return $user->hasRole("superadmin") ? true : null;
        });
    }
}
