<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-outline-primary me-3" wire:click.prevent="goToListUser()">
                        <i class="fa fa-arrow-left me-1"></i> Retour
                    </button>
                    <i class="fa fa-user-edit me-2 text-primary"></i>
                    Modifier l'utilisateur
                </h1>
                <p class="fs-sm fw-medium text-muted mb-0">
                    Modifier les informations de {{ $editUser['nom'] ?? '' }} {{ $editUser['prenom'] ?? '' }}
                </p>
            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">
                            <i class="fa fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Modification
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <div class="row">
        <!-- Formulaire principal -->
        <div class="col-lg-8">
            <form method="POST" role="form" wire:submit.prevent="updateUser()" enctype="multipart/form-data">
                <!-- Informations personnelles -->
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-user me-2"></i>Informations personnelles
                        </h3>
                        <div class="block-options">
                            <span class="badge bg-info">Modification</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_nom">
                                    Nom <span class="text-danger">*</span>
                                </label>
                                <input type="text" wire:model.lazy="editUser.nom"
                                    class="form-control @error('editUser.nom') is-invalid @enderror"
                                    id="edit_nom" placeholder="Nom de famille">
                                @error('editUser.nom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_prenom">
                                    Prénom <span class="text-danger">*</span>
                                </label>
                                <input type="text" wire:model.lazy="editUser.prenom"
                                    class="form-control @error('editUser.prenom') is-invalid @enderror"
                                    id="edit_prenom" placeholder="Prénom">
                                @error('editUser.prenom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_sexe">
                                    Sexe <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('editUser.sexe') is-invalid @enderror"
                                    wire:model="editUser.sexe" id="edit_sexe">
                                    <option value="">Sélectionnez le sexe</option>
                                    <option value="H">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                @error('editUser.sexe')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_email">
                                    Adresse email <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fa fa-envelope"></i>
                                    </span>
                                    <input type="email" wire:model.lazy="editUser.email"
                                        class="form-control @error('editUser.email') is-invalid @enderror"
                                        id="edit_email" placeholder="<EMAIL>">
                                </div>
                                @error('editUser.email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_telephone1">
                                    Téléphone principal <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fa fa-phone"></i>
                                    </span>
                                    <input type="tel" wire:model.lazy="editUser.telephone1"
                                        class="form-control @error('editUser.telephone1') is-invalid @enderror"
                                        id="edit_telephone1" placeholder="+261 XX XX XXX XX">
                                </div>
                                @error('editUser.telephone1')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations de paiement -->
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-credit-card me-2"></i>Informations de paiement
                        </h3>
                    </div>
                    <div class="block-content">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_reference">
                                    Référence paiement
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fa fa-receipt"></i>
                                    </span>
                                    <input type="text" wire:model.lazy="editUser.reference"
                                        class="form-control @error('editUser.reference') is-invalid @enderror"
                                        id="edit_reference" placeholder="Référence de paiement">
                                </div>
                                @error('editUser.reference')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_montantenvoi">
                                    Montant
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fa fa-money-bill"></i>
                                    </span>
                                    <input type="number" wire:model.lazy="editUser.montantenvoi"
                                        class="form-control @error('editUser.montantenvoi') is-invalid @enderror"
                                        id="edit_montantenvoi" placeholder="Montant en Ariary" step="0.01">
                                    <span class="input-group-text">Ar</span>
                                </div>
                                @error('editUser.montantenvoi')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_telenvoi">
                                    Numéro d'envoi
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fa fa-mobile-alt"></i>
                                    </span>
                                    <input type="tel" wire:model.lazy="editUser.telenvoi"
                                        class="form-control @error('editUser.telenvoi') is-invalid @enderror"
                                        id="edit_telenvoi" placeholder="Numéro d'envoi">
                                </div>
                                @error('editUser.telenvoi')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations académiques -->
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-graduation-cap me-2"></i>Informations académiques
                        </h3>
                    </div>
                    <div class="block-content">
                        <div class="row g-4">



                            <div class="col-md-6">
                                <label class="form-label" for="edit_mention_id">
                                    Mention
                                </label>
                                <select class="form-select @error('editUser.mention_id') is-invalid @enderror"
                                    wire:model="editUser.mention_id" id="edit_mention_id">
                                    <option value="">Sélectionnez une mention</option>
                                    @foreach ($mentions as $mention)
                                        <option value="{{ $mention->id }}">{{ $mention->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editUser.mention_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_parcour_id">
                                    Parcours
                                </label>
                                <select class="form-select @error('editUser.parcour_id') is-invalid @enderror"
                                    wire:model="editUser.parcour_id" id="edit_parcour_id">
                                    <option value="">Sélectionnez un parcours</option>
                                    @foreach ($parcours as $parcour)
                                        <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editUser.parcour_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_niveau_id">
                                    Niveau
                                </label>
                                <select class="form-select @error('editUser.niveau_id') is-invalid @enderror"
                                    wire:model="editUser.niveau_id" id="edit_niveau_id">
                                    <option value="">Sélectionnez un niveau</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editUser.niveau_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label" for="edit_annee_universitaire_id">
                                    Année universitaire
                                </label>
                                <select class="form-select @error('editUser.annee_universitaire_id') is-invalid @enderror"
                                    wire:model="editUser.annee_universitaire_id" id="edit_annee_universitaire_id">
                                    <option value="">Sélectionnez une année</option>
                                    @foreach ($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editUser.annee_universitaire_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                           

                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="block block-rounded">
                    <div class="block-content">
                        <div class="d-flex justify-content-between align-items-center">
                            <button type="button" class="btn btn-outline-secondary" wire:click.prevent="goToListUser()">
                                <i class="fa fa-times me-1"></i> Annuler
                            </button>
                            <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
                                <span wire:loading.remove>
                                    <i class="fa fa-save me-1"></i> Enregistrer les modifications
                                </span>
                                <span wire:loading>
                                    <i class="fa fa-spinner fa-spin me-1"></i> Enregistrement...
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sidebar avec rôles et actions -->
        <div class="col-lg-4">
            <!-- Rôles et permissions -->
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-shield-alt me-2"></i>Rôles et permissions
                    </h3>
                </div>
                <div class="block-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle me-2"></i>
                        <small>Sélectionnez les rôles à attribuer à cet utilisateur.</small>
                    </div>

                    @foreach ($rolePermissions['roles'] as $role)
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                    wire:model.lazy="rolePermissions.roles.{{ $loop->index }}.active"
                                    id="role_{{ $role['role_id'] }}"
                                    @if ($role['active']) checked @endif>
                                <label class="form-check-label fw-semibold" for="role_{{ $role['role_id'] }}">
                                    {{ $role['role_nom'] }}
                                </label>
                            </div>
                        </div>
                    @endforeach

                    <div class="d-grid">
                        <button class="btn btn-primary" wire:click="updateRoleAndPermissions" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <i class="fa fa-check me-1"></i> Appliquer les rôles
                            </span>
                            <span wire:loading>
                                <i class="fa fa-spinner fa-spin me-1"></i> Application...
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Réinitialisation du mot de passe -->
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-key me-2"></i>Mot de passe
                    </h3>
                </div>
                <div class="block-content">
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle me-2"></i>
                        <small>Laissez vide pour conserver le mot de passe actuel.</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" for="new_password">
                            Nouveau mot de passe
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-lock"></i>
                            </span>
                            <input type="password" wire:model="newPasswd"
                                class="form-control" id="new_password"
                                placeholder="Nouveau mot de passe">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="fa fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button class="btn btn-warning" wire:click="resetPassword"
                                wire:loading.attr="disabled" @if(!$newPasswd) disabled @endif>
                            <span wire:loading.remove>
                                <i class="fa fa-key me-1"></i> Réinitialiser
                            </span>
                            <span wire:loading>
                                <i class="fa fa-spinner fa-spin me-1"></i> Réinitialisation...
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Informations utilisateur -->
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-info-circle me-2"></i>Informations
                    </h3>
                </div>
                <div class="block-content">
                    <div class="row g-3">
                        <div class="col-12">
                            <small class="text-muted">ID utilisateur:</small>
                            <div class="fw-semibold">#{{ $editUser['id'] ?? 'N/A' }}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Créé le:</small>
                            <div class="fw-semibold">
                                @if(isset($editUser['created_at']))
                                    {{ \Carbon\Carbon::parse($editUser['created_at'])->format('d/m/Y à H:i') }}
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Dernière modification:</small>
                            <div class="fw-semibold">
                                @if(isset($editUser['updated_at']))
                                    {{ \Carbon\Carbon::parse($editUser['updated_at'])->format('d/m/Y à H:i') }}
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Pièces jointes -->
        <div class="col-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-paperclip me-2"></i>Pièces jointes
                    </h3>
                    <div class="block-options">
                        <span class="badge bg-secondary">{{ count($mediaItems) }} fichier(s)</span>
                    </div>
                </div>
                <div class="block-content">
                    @forelse ($mediaItems as $media)
                        <div class="d-flex align-items-center push border-bottom pb-3 mb-3">
                            <div class="flex-shrink-0 me-3">
                                <div class="item item-rounded bg-primary">
                                    <i class="fa fa-file text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold">{{ $media->collection_name }}</div>
                                <div class="fs-sm text-muted">
                                    {{ $media->name }}
                                    @if($media->size)
                                        ({{ number_format($media->size / 1024, 2) }} KB)
                                    @endif
                                </div>
                                <div class="fs-sm text-muted">
                                    Ajouté le {{ $media->created_at->format('d/m/Y à H:i') }}
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <a class="btn btn-sm btn-outline-primary"
                                   href="{{ route('admin.habilitations.download', ['id' => $editUser['id'], 'collection' => $media->collection_name]) }}"
                                   title="Télécharger">
                                    <i class="fa fa-download"></i>
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fa fa-file-o fa-3x text-muted mb-3"></i>
                            <div class="text-muted">Aucune pièce jointe disponible</div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function togglePassword() {
        const passwordInput = document.getElementById('new_password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
</script>
