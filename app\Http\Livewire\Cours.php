<?php

namespace App\Http\Livewire;

use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Ue;
use App\Models\User;
use Illuminate\Contracts\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class Cours extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;
    
    public $newCours = [];
    public $editCours = [];
    public $query;
    public $filtreParcours;
    public $filtreNiveau;
    public Ue $current_ue;

    // public $parcours;
    // public $parcour;

    // public $ues;
    // public $ue;

    public function updatingQuery(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }

    public function mount($ueId)
    {
        $this->current_ue = Ue::find($ueId);
        // $this->parcours = Parcour::all(['id', 'sigle']);
        // $this->ues = collect();
    }

    public function render()
    {
        $persQuery = Matiere::query()->with(['user', 'ue'])->whereUeId($this->current_ue->id);

        if($this->query != ""){
            $persQuery->where(function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreParcours != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        if($this->filtreNiveau != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }

        return view('livewire.deraq.cours.index', [
            "cours" => $persQuery->latest()->paginate(5),
            "enseignants" => User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
            ->get(['id', 'nom', 'prenom']),
            "niveaux" => Niveau::all(),
            "ues" => Ue::all(),
            "parcours" => Parcour::all()
            // "ues" => Ue::all(['id', 'code', 'nom'])
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    // public function updatedParcour($value)
    // {
    //     $this->ues = Ue::where('parcour_id', $value)->get();
    //     $this->ue = $this->ues->first()->id ?? null;
    // }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editCours['id'])
            return [
                'editCours.nom' => 'required',
                'editCours.code' => 'required',
                'editCours.ue_id' => 'required',
                'editCours.user_id' => 'required|unique:matieres,code',
            ];
        }

        return [
            'newCours.nom' => 'required',
            'newCours.user_id' => 'required',
            'newCours.code' => 'required|unique:matieres,code',
        ];
    }

   

    public function goToListCours()
    {
        $this->currentPage = PAGELIST;
        $this->newCours = [];
    }

    public function goToAddCours()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditCours(Matiere $cours){
        $this->editCours = $cours->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addCours()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dd($validationAttributes);
        
        // Ajouter un nouvel utilisateur
        Matiere::create([
            "nom" => $validationAttributes["newCours"]["nom"],
            "code" => $validationAttributes["newCours"]["code"],
            "user_id" => $validationAttributes["newCours"]["user_id"],
            "ue_id" => $this->current_ue->id,
        ]);

        $this->newCours = [];
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Cours créé avec succès!"]);
        // dump($pass);
        $this->goToListCours();
    }

    public function updateCours(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dd($validationAttributes);

        Matiere::find($this->editCours["id"])->update([
            "nom" => $validationAttributes["editCours"]["nom"],
            "code" => $validationAttributes["editCours"]["code"],
            "ue_id" => $validationAttributes["editCours"]["ue_id"],
            "user_id" => $validationAttributes["editCours"]["user_id"],
        ]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Cours mis à jour avec succès!"]);
        $this->editCours = [];
        $this->goToListCours();
    }

    public function deleteCours($id)
    {
        Matiere::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Cours supprimé avec succès!"]);
    }
}
