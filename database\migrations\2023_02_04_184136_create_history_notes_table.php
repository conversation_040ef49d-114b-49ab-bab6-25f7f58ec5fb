<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId("matiere_id")->constrained();
            $table->foreignId("type_note_id")->constrained();
            $table->foreignId("user_id")->constrained();
            $table->boolean("estModifiable")->default(0);
            $table->boolean("is_valid")->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('history_notes', function (Blueprint $table) {
            $table->dropForeign("user_id");
            $table->dropForeign("matiere_id");
            $table->dropForeign("type_note_id");
        });
        Schema::dropIfExists('history_notes');
    }
};
