<?php
use Illuminate\Support\Str;

define("PAGELIST", "liste");
define("PAGECREATEFORM", "create");
define("PAGEEDITFORM", "edit");
define("PAGEOTHERFORM", "other");


function userFullName()
{
    return auth()->user()->prenom . " " . auth()->user()->nom;
}

function getRolesName(){
    $rolesName = "";
    $i = 0;
    foreach(auth()->user()->roles as $role){
        $rolesName .= $role->nom;

        //
        if($i < sizeof(auth()->user()->roles) - 1 ){
            $rolesName .= ",";
        }

        $i++;

    }

    return $rolesName;

}

function getMatieresName(){
    $matieresName = "";
    $i = 0;
    foreach(auth()->user()->matieres as $matiere){
        $matieresName .= $matiere->nom;

        //
        if($i < sizeof(auth()->user()->matieres) - 1 ){
            $matieresName .= ",";
        }

        $i++;

    }

    return $matieresName;

}

function setMenuClass($route, $classe){
    $routeActuel = request()->route()->getName();

    if(contains($routeActuel, $route) ){
        return $classe;
    }
    return "";
}

function setMenuActive($route){
    $routeActuel = request()->route()->getName();

    if($routeActuel === $route ){
        return "active";
    }
    return "";
}

function contains($container, $contenu){
    return Str::contains($container, $contenu);
}


function containsValueLessThanFive($array)
{
    $containsLessThanFive = false;

    array_walk_recursive($array, function ($value) use (&$containsLessThanFive) {
        if ($value < 5) {
            $containsLessThanFive = true;
        }
    });

    return $containsLessThanFive;
}