<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Note;
use App\Models\Semestre;
use App\Models\TypeNote;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class NoteEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;
    
    public $newCours = [];
    public $editCours = [];
    public $query;
    public $filtreTypes;
    public $filtreAnnee;
    public $filtreSemestre;
    public User $current_user;

    // public $parcours;
    // public $parcour;

    // public $ues;
    // public $ue;

    public function updatingQuery(){
        $this->resetPage();
    }

    public function mount($userId)
    {
        $this->current_user = User::find($userId);
    }

    public function render()
    {
        $persQuery = Note::query()->with(['user', 'ue', 'matiere'])->whereUserId($this->current_user->id);

        if($this->query != ""){
            $persQuery->whereHas('matiere', function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreTypes != ""){
            $persQuery->whereTypeNoteId($this->filtreTypes);
        }
        if($this->filtreAnnee != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }
        if($this->filtreSemestre != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereSemestreId($this->filtreSemestre));
        }

        return view('livewire.deraq.noteetu.index', [
            "notes" => $persQuery->paginate(10),
            "annees" => AnneeUniversitaire::all(),
            "types" => TypeNote::all(),
            "semestres" => Semestre::all(),

            // "ues" => Ue::all(['id', 'code', 'nom'])
        ])
        ->extends('layouts.backend')
        ->section('content');
    }
}
