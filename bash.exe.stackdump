Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA100, 0007FFFF9000) msys-2.0.dll+0x1FEBA
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210285FF9, 0007FFFF9FB8, 0007FFFFA100, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA100  0002100690B4 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA3E0  00021006A49D (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD61790000 ntdll.dll
7FFD605F0000 KERNEL32.DLL
7FFD5F010000 KERNELBASE.dll
7FFD606C0000 USER32.dll
7FFD5EFE0000 win32u.dll
7FFD60D80000 GDI32.dll
7FFD5EC10000 gdi32full.dll
7FFD5EF40000 msvcp_win.dll
7FFD5E880000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD61690000 advapi32.dll
7FFD61510000 msvcrt.dll
7FFD60070000 sechost.dll
7FFD5F3F0000 bcrypt.dll
7FFD60130000 RPCRT4.dll
7FFD5E100000 CRYPTBASE.DLL
7FFD5EB10000 bcryptPrimitives.dll
7FFD614D0000 IMM32.DLL
