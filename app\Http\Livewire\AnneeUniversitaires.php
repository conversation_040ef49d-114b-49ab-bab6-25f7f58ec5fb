<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use Livewire\Component;

class AnneeUniversitaires extends Component
{
    public $currentPage = PAGELIST;

    public $newAnnee = [];
    public $editAnnee = [];
    
    public function render()
    {
        return view('livewire.deraq.annee.index', [
            "annees" => AnneeUniversitaire::all()
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editAnnee.nom' => 'required',
            ];
        }

        return [
            'newAnnee.nom' => 'required',
            
        ];
    }

    public function goToListAnnee()
    {
        $this->currentPage = PAGELIST;
        $this->newAnnee = [];
    }

    public function goToAddAnnee()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditAnnee(AnneeUniversitaire $Annee){
        $this->editAnnee = $Annee->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addAnnee()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        AnneeUniversitaire::create($validationAttributes["newAnnee"]);

        $this->newAnnee = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Annee créé avec succès!"]);
        // dump($pass);
        $this->goToListAnnee();
    }

    public function updateAnnee(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        AnneeUniversitaire::find($this->editAnnee["id"])->update($validationAttributes["editAnnee"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Annee mis à jour avec succès!"]);
        $this->goToListAnnee();
    }

    public function deleteAnnee($id)
    {
        AnneeUniversitaire::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Annee supprimé avec succès!"]);
    }
}
