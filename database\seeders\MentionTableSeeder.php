<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MentionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("mentions")->insert([
            // ["nom" => "Management Des Operations Bancaires Et Assurances", "domaine_id" => 1],
            // ["nom" => "Management des Opérations du Commerce International", "domaine_id" => 1],
            // ["nom" => "Management des Opération Financières Comptabilité et Audit", "domaine_id" => 1],
            // ["nom" => "Tourisme hôtellerie", "domaine_id" => 1],
            // ["nom" => "Management Communication et Marketing", "domaine_id" => 1],
            // ["nom" => "Génie Electrique et Informatique Industrielle", "domaine_id" => 2],
            // ["nom" => "Génie Civil", "domaine_id" => 2],
            // ["nom" => "Génie Mécanique", "domaine_id" => 2],
            // ["nom" => "Administration Des Etablissements De Santé", "domaine_id" => 1],
            ["nom" => "Management des affaires", "domaine_id" => 1],
            ["nom" => "Génie Logiciel et Systèmes", "domaine_id" => 2],
        ]);
    }
}
