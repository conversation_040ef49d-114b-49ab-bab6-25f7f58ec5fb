<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string("nom")->nullable();
            $table->string("prenom")->nullable();
            $table->char('sexe')->nullable();
            $table->string("date_naissance")->nullable();
            $table->string("lieu_naissance")->nullable();
            $table->string("nationalite")->nullable();
            $table->string("ville")->nullable();
            $table->string("pays")->nullable();
            $table->string("adresse")->nullable();
            $table->string("telephone1")->nullable();
            $table->string("telephone2")->nullable();

            $table->string("nom_pere")->nullable();
            $table->string("nom_mere")->nullable();
            
            $table->string("cin")->nullable();
            $table->string("date_delivrance")->nullable();
            $table->string("lieu_delivrance")->nullable();
            $table->string("duplicata")->nullable();

            $table->string("matricule")->nullable()->unique();
            $table->string('email')->nullable()->unique();
            $table->string('password')->nullable();
            $table->string('photo')->nullable();
            $table->string('inscription_date')->nullable();
            
            $table->foreignId("parcour_id")->nullable()->constrained();
            $table->foreignId("niveau_id")->nullable()->constrained();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign("parcour_id");
            $table->dropForeign("niveau_id");
        });

        Schema::dropIfExists('users');
    }
};
