{"__meta": {"id": "Xa91c3916d6c1dbf487ee11bb11aaa175", "datetime": "2025-07-01 17:19:16", "utime": 1751379556.488905, "method": "POST", "uri": "/livewire/message/utilisateurs", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379554.134225, "end": 1751379556.488939, "duration": 2.3547141551971436, "duration_str": "2.35s", "measures": [{"label": "Booting", "start": 1751379554.134225, "relative_start": 0, "end": 1751379554.69868, "relative_end": 1751379554.69868, "duration": 0.5644550323486328, "duration_str": "564ms", "params": [], "collector": null}, {"label": "Application", "start": 1751379554.699423, "relative_start": 0.5651981830596924, "end": 1751379556.488942, "relative_end": 2.86102294921875e-06, "duration": 1.7895188331604004, "duration_str": "1.79s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27909696, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.utilisateurs.index (\\resources\\views\\livewire\\utilisateurs\\index.blade.php)", "param_count": 30, "params": ["users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "errors", "_instance", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/index.blade.php&line=0"}, {"name": "livewire.utilisateurs.edit (\\resources\\views\\livewire\\utilisateurs\\edit.blade.php)", "param_count": 32, "params": ["__env", "app", "errors", "_instance", "users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/edit.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.14075000000000001, "accumulated_duration_str": "141ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00511, "duration_str": "5.11ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 3.631}, {"sql": "select * from `users` where `users`.`id` = 555 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 377}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:377", "connection": "inscriptionimsaa", "start_percent": 3.631, "width_percent": 0.639}, {"sql": "select * from `media` where `media`.`model_id` in (555) and `media`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 23, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 24, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 382}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.01044, "duration_str": "10.44ms", "stmt_id": "\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php:545", "connection": "inscriptionimsaa", "start_percent": 4.27, "width_percent": 7.417}, {"sql": "select * from `users` where `users`.`id` = 555 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 399}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 387}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:399", "connection": "inscriptionimsaa", "start_percent": 11.687, "width_percent": 0.98}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 555", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 399}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 387}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:399", "connection": "inscriptionimsaa", "start_percent": 12.668, "width_percent": 1.108}, {"sql": "select * from `users` where `users`.`id` = 555 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 400}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 387}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:400", "connection": "inscriptionimsaa", "start_percent": 13.776, "width_percent": 1.094}, {"sql": "select `permissions`.*, `user_permission`.`user_id` as `pivot_user_id`, `user_permission`.`permission_id` as `pivot_permission_id` from `permissions` inner join `user_permission` on `permissions`.`id` = `user_permission`.`permission_id` where `user_permission`.`user_id` = 555", "type": "query", "params": [], "bindings": ["555"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 400}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 387}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11634, "duration_str": "116ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:400", "connection": "inscriptionimsaa", "start_percent": 14.87, "width_percent": 82.657}, {"sql": "select * from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 402}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 387}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:402", "connection": "inscriptionimsaa", "start_percent": 97.528, "width_percent": 0.455}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 97.982, "width_percent": 0.618}, {"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where `users`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 98.6, "width_percent": 0.647}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (85, 86, 87, 88, 89, 90, 91, 92, 554, 555)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 99.247, "width_percent": 0.753}]}, "models": {"data": {"App\\Models\\Role": 18, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": 1, "App\\Models\\User": 14}, "count": 33}, "livewire": {"data": {"utilisateurs #CYJU06vnXpc2IFNU82zJ": "array:5 [\n  \"data\" => array:17 [\n    \"currentPage\" => \"edit\"\n    \"newUser\" => []\n    \"editUser\" => array:36 [\n      \"id\" => 555\n      \"nom\" => \"Katell\"\n      \"prenom\" => \"Schmidt\"\n      \"sexe\" => \"F\"\n      \"date_naissance\" => \"1994-11-11\"\n      \"lieu_naissance\" => \"+****************\"\n      \"nationalite\" => \"+****************\"\n      \"ville\" => null\n      \"pays\" => null\n      \"adresse\" => \"+****************\"\n      \"telephone1\" => \"+261325551650\"\n      \"telephone2\" => \"+****************\"\n      \"nom_pere\" => null\n      \"nom_mere\" => null\n      \"cin\" => \"+****************\"\n      \"date_delivrance\" => \"1972-04-24\"\n      \"lieu_delivrance\" => \"+****************\"\n      \"duplicata\" => null\n      \"matricule\" => null\n      \"email\" => \"<EMAIL>\"\n      \"photo\" => \"media/avatars/avatar0.jpg\"\n      \"inscription_date\" => null\n      \"parcour_id\" => null\n      \"niveau_id\" => 1\n      \"created_at\" => \"2025-05-12T12:33:50.000000Z\"\n      \"updated_at\" => \"2025-05-12T12:33:50.000000Z\"\n      \"deleted_at\" => null\n      \"is_filled\" => 0\n      \"tel_pere\" => null\n      \"tel_mere\" => null\n      \"nom_tuteur\" => null\n      \"tel_tuteur\" => null\n      \"montantenvoi\" => \"21314\"\n      \"reference\" => \"13241234\"\n      \"telenvoi\" => \"132423\"\n      \"mention_id\" => 1\n    ]\n    \"mediaItems\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#1909\n      #items: array:1 [\n        0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2233\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 84\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 555\n            \"uuid\" => \"5afcb7cc-b37d-496d-823f-34b0d7409948\"\n            \"collection_name\" => \"Releve_ou_Diplome_Bacc\"\n            \"name\" => \"etat_paiement_1747034417\"\n            \"file_name\" => \"etat_paiement_1747034417.pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 89752\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2025-05-12 15:33:52\"\n            \"updated_at\" => \"2025-05-12 15:33:52\"\n          ]\n          #original: array:18 [\n            \"id\" => 84\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 555\n            \"uuid\" => \"5afcb7cc-b37d-496d-823f-34b0d7409948\"\n            \"collection_name\" => \"Releve_ou_Diplome_Bacc\"\n            \"name\" => \"etat_paiement_1747034417\"\n            \"file_name\" => \"etat_paiement_1747034417.pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 89752\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2025-05-12 15:33:52\"\n            \"updated_at\" => \"2025-05-12 15:33:52\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n      +collectionName: null\n      +formFieldName: null\n    }\n    \"isEtu\" => false\n    \"query\" => \"\"\n    \"newPasswd\" => null\n    \"filterRole\" => \"\"\n    \"filterStatus\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"selectedUsers\" => []\n    \"selectAll\" => false\n    \"rolePermissions\" => array:2 [\n      \"roles\" => array:7 [\n        0 => array:3 [\n          \"role_id\" => 1\n          \"role_nom\" => \"superadmin\"\n          \"active\" => false\n        ]\n        1 => array:3 [\n          \"role_id\" => 2\n          \"role_nom\" => \"enseignant\"\n          \"active\" => false\n        ]\n        2 => array:3 [\n          \"role_id\" => 3\n          \"role_nom\" => \"deraq\"\n          \"active\" => false\n        ]\n        3 => array:3 [\n          \"role_id\" => 4\n          \"role_nom\" => \"secretaire\"\n          \"active\" => false\n        ]\n        4 => array:3 [\n          \"role_id\" => 5\n          \"role_nom\" => \"etudiant\"\n          \"active\" => true\n        ]\n        5 => array:3 [\n          \"role_id\" => 6\n          \"role_nom\" => \"admin\"\n          \"active\" => false\n        ]\n        6 => array:3 [\n          \"role_id\" => 7\n          \"role_nom\" => \"caf\"\n          \"active\" => false\n        ]\n      ]\n      \"permissions\" => []\n    ]\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"utilisateurs\"\n  \"view\" => \"livewire.utilisateurs.index\"\n  \"component\" => \"App\\Http\\Livewire\\Utilisateurs\"\n  \"id\" => \"CYJU06vnXpc2IFNU82zJ\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/habilitations/utilisateurs\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]"}, "request": {"path_info": "/livewire/message/utilisateurs", "status_code": "<pre class=sf-dump id=sf-dump-1457153649 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1457153649\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1449888813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1449888813\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2011703092 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">CYJU06vnXpc2IFNU82zJ</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">utilisateurs</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"26 characters\">habilitations/utilisateurs</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">93f83d02</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:17</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>mediaItems</span>\" => []\n      \"<span class=sf-dump-key>isEtu</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>newPasswd</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filterRole</span>\" => \"\"\n      \"<span class=sf-dump-key>filterStatus</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>selectedUsers</span>\" => []\n      \"<span class=sf-dump-key>selectAll</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>rolePermissions</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e1e083b89c5789797778f58d315e286eb494fc9e35b95189b853397c18af1042</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ecso</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">goToEditUser</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>555</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011703092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2064731633 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">696</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkpmdXJRdnJuOGRjbFlxM2Z0QXh2WXc9PSIsInZhbHVlIjoiSVNZZDNEaXR4NXpZeUN6Q0xBVm9iSHpOTVRkWEZvT1B0K0I4cGVaZ2swTjhkMzYwY2FMVEQ3YXB0eWNhZlN0YnBZYjdLbjVHWGp5eDZaMkRDeVJxVG9NWUtMVVVJZzIvWGJKN08wZFM2Y3VQdG1xZmliMFZTNjE1SEZWTmdFZG8iLCJtYWMiOiIyNDE1N2Y5Y2U5Nzk1NWYzMGUyNTI3M2E4NGI3MTQxOTdjMTM5N2E3MGI0ZjdiNWFiMTc0OGYyNTkxMmE4NmM1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Im8xU3NYd2daOGRjUHNGaVA5b1FGSFE9PSIsInZhbHVlIjoiOHZPdktOVWR1cmU2aDV3d0YxTU5ZZHNmU1FZZ0d1d0g4ZHIySVNnSFlCWTZoZG9vMmpXcWlTR3M0V2s0VzQ4dUV3WmZqUVFWYjgzY0NNRThXNFZoTUU0QmtmR2tLYXk4S2FpZVVFZWJxbFJzZWIycjRCdEt3eHY0SkphUnVNcnkiLCJtYWMiOiIyYWJkYTgxOTQ3M2NkNDlkNzk3YWE5NGYyNDM2YTQ5MjE5NzE3OTZhM2Y5OTEyMTJhM2IyYTkyMGI3NmZjNWE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064731633\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1683232826 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56777</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">696</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">696</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkpmdXJRdnJuOGRjbFlxM2Z0QXh2WXc9PSIsInZhbHVlIjoiSVNZZDNEaXR4NXpZeUN6Q0xBVm9iSHpOTVRkWEZvT1B0K0I4cGVaZ2swTjhkMzYwY2FMVEQ3YXB0eWNhZlN0YnBZYjdLbjVHWGp5eDZaMkRDeVJxVG9NWUtMVVVJZzIvWGJKN08wZFM2Y3VQdG1xZmliMFZTNjE1SEZWTmdFZG8iLCJtYWMiOiIyNDE1N2Y5Y2U5Nzk1NWYzMGUyNTI3M2E4NGI3MTQxOTdjMTM5N2E3MGI0ZjdiNWFiMTc0OGYyNTkxMmE4NmM1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Im8xU3NYd2daOGRjUHNGaVA5b1FGSFE9PSIsInZhbHVlIjoiOHZPdktOVWR1cmU2aDV3d0YxTU5ZZHNmU1FZZ0d1d0g4ZHIySVNnSFlCWTZoZG9vMmpXcWlTR3M0V2s0VzQ4dUV3WmZqUVFWYjgzY0NNRThXNFZoTUU0QmtmR2tLYXk4S2FpZVVFZWJxbFJzZWIycjRCdEt3eHY0SkphUnVNcnkiLCJtYWMiOiIyYWJkYTgxOTQ3M2NkNDlkNzk3YWE5NGYyNDM2YTQ5MjE5NzE3OTZhM2Y5OTEyMTJhM2IyYTkyMGI3NmZjNWE5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379554.1342</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379554</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683232826\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-375048802 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:19:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375048802\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1824905500 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824905500\", {\"maxDepth\":0})</script>\n"}}