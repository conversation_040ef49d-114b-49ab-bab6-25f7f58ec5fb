<?php

namespace App\Http\Livewire;

use App\Mail\SignUp;
use App\Models\Parcour;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Livewire\WithPagination;

class Enseignant extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newUser = [];
    public $editUser = [];
    public $query;
    public $filtreParcours;

    public function updatingQuery(){
        $this->resetPage();
    }

    public function render()
    {
        Carbon::setLocale("fr");
        
        $persQuery = User::query();

        if($this->query != ""){
            $persQuery->where(function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('prenom', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreParcours != ""){
            $persQuery->whereHas('matieres.ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }

        return view('livewire.deraq.enseignant.index', [
            "enseignants" => $persQuery->whereRelation('roles', 'role_id', '=', 2)->latest()
            ->paginate(10),
            "parcours" => Parcour::all()
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUser.nom' => 'required',
                'editUser.prenom' => 'required',
                'editUser.email' => ['required', 'email', Rule::unique("users", "email")->ignore($this->editUser['id'])],
                'editUser.telephone1' => ['numeric', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])],
                'editUser.sexe' => 'required',
            ];
        }

        return [
            // 'newUser.nom' => 'required',
            'newUser.prenom' => 'required',
            'newUser.email' => 'email|unique:users,email',
            'newUser.sexe' => 'required',
            'newUser.telephone1' => 'numeric|unique:users,telephone1',
            
        ];
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        $this->editUser = [];
        // return redirect(request()->header('Referer'));
    }

    public function goToEditUser($id)
    {
        $this->editUser = User::find($id)->toArray();
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
    }
    
    public function goToAddUser()
    {
        $this->currentPage = PAGECREATEFORM;
    }


    public function addUser()
    {
        $pass = Str::random(10);
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";
        $validationAttributes["newUser"]["password"] = Hash::make($pass);

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        $user = User::create($validationAttributes["newUser"]);

        $user->roles()->attach(2);

        // Mail::to($this->newUser["email"])->send(new SignUp($this->newUser["nom"], $this->newUser["prenom"], $pass, $this->newUser["email"]));

        $this->newUser = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur créé avec succès!"]);
        // dump($pass);
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        User::find($this->editUser["id"])->update($validationAttributes["editUser"]);

        // Mail::to($this->newUser["email"])->send(new SignUp($this->newUser["nom"], $this->newUser["prenom"], $pass, $this->newUser["email"]));

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur mis à jour avec succès!"]);
    }

    public function deleteUser($id)
    {
        User::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur supprimé avec succès!"]);
    }
}
