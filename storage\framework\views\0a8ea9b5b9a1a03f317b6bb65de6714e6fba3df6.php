<?php $__env->startSection('css'); ?>
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')); ?>">

    <style>
        /* Progress Wizard Styles */
        .progress-wizard {
            padding: 20px 0;
        }

        .progress-wizard .step {
            text-align: center;
            position: relative;
            flex: 1;
        }

        .progress-wizard .step:not(:last-child):after {
            content: '';
            position: absolute;
            top: 25px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .progress-wizard .step.active:not(:last-child):after {
            background: #0d6efd;
        }

        .progress-wizard .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .progress-wizard .step.active .step-icon {
            background: #0d6efd;
            color: white;
        }

        .progress-wizard .step-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6c757d;
        }

        .progress-wizard .step.active .step-title {
            color: #0d6efd;
            font-weight: 600;
        }

        /* Loading states */
        .table-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Enhanced form styles */
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .invalid-feedback {
            display: block;
        }

        /* Card hover effects */
        .block:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transition: box-shadow 0.15s ease-in-out;
        }

        /* Badge improvements */
        .badge {
            font-size: 0.75rem;
        }

        /* Button group improvements */
        .btn-group .btn {
            border-radius: 0.375rem;
            margin-right: 0.25rem;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        /* Table improvements */
        .table-responsive {
            border-radius: 0.375rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table-active {
            background-color: rgba(13, 110, 253, 0.1);
        }

        /* Avatar improvements */
        .img-avatar32 {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Empty state */
        .empty-state {
            padding: 3rem 1rem;
            text-align: center;
        }

        .empty-state i {
            opacity: 0.5;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            /* Hero section mobile */
            .content-full .d-flex {
                flex-direction: column !important;
                align-items: flex-start !important;
            }

            .content-full .flex-shrink-0 {
                margin-top: 1rem !important;
                margin-left: 0 !important;
                width: 100%;
            }

            .content-full .d-flex .gap-2 {
                width: 100%;
                justify-content: space-between;
            }

            /* Statistics cards mobile */
            .row.mb-4 .col-lg-3 {
                margin-bottom: 1rem;
            }

            /* Filters mobile */
            .block-content .row.g-3 {
                margin-bottom: 1rem;
            }

            .block-content .row.g-3 .col-md-4,
            .block-content .row.g-3 .col-md-2 {
                margin-bottom: 0.75rem;
            }

            /* Table mobile improvements */
            .table-responsive {
                border: none;
                box-shadow: none;
            }

            .table th, .table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.875rem;
            }

            .table .btn-group {
                flex-direction: column;
                gap: 0.25rem;
            }

            .table .btn-group .btn {
                margin-right: 0;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }

            /* Form mobile improvements */
            .progress-wizard .step {
                flex-direction: column;
                margin-bottom: 1rem;
            }

            .progress-wizard .step:not(:last-child):after {
                display: none;
            }

            .progress-wizard .step-icon {
                width: 40px;
                height: 40px;
                margin-bottom: 0.5rem;
            }

            .progress-wizard .step-title {
                font-size: 0.75rem;
            }

            /* Block headers mobile */
            .block-header {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 0.5rem;
            }

            .block-options {
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }

            /* Form groups mobile */
            .row.g-4 .col-md-6,
            .row.g-4 .col-md-4,
            .row.g-4 .col-md-2 {
                margin-bottom: 1rem;
            }

            /* Button groups mobile */
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 1rem;
            }

            .d-flex.justify-content-between > div {
                width: 100%;
                display: flex;
                gap: 0.5rem;
            }

            /* Pagination mobile */
            .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination .page-item {
                margin: 0.125rem;
            }

            /* Alert mobile */
            .alert {
                font-size: 0.875rem;
                padding: 0.75rem;
            }

            /* Sidebar mobile (edit form) */
            .col-lg-8, .col-lg-4 {
                margin-bottom: 1rem;
            }

            /* Input groups mobile */
            .input-group-text {
                min-width: 40px;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            /* Extra small devices */
            .content {
                padding: 1rem 0.5rem;
            }

            .block {
                margin-bottom: 1rem;
            }

            .block-content {
                padding: 1rem;
            }

            .table th, .table td {
                padding: 0.375rem 0.125rem;
                font-size: 0.8rem;
            }

            .btn {
                font-size: 0.8rem;
                padding: 0.375rem 0.75rem;
            }

            .btn-sm {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }

            .form-control, .form-select {
                font-size: 0.875rem;
            }

            .badge {
                font-size: 0.7rem;
            }

            /* Hide less important columns on very small screens */
            .d-none.d-lg-table-cell {
                display: none !important;
            }

            /* Stack form buttons */
            .d-flex.gap-2 {
                flex-direction: column;
                gap: 0.5rem !important;
            }

            .d-flex.gap-2 .btn {
                width: 100%;
            }
        }

        /* Touch improvements */
        @media (hover: none) and (pointer: coarse) {
            .btn, .form-control, .form-select {
                min-height: 44px; /* Minimum touch target size */
            }

            .table .btn {
                min-height: 36px;
                min-width: 36px;
            }

            .form-check-input {
                width: 1.25rem;
                height: 1.25rem;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <!-- jQuery (required for DataTables plugin) -->
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>
    
    <script src="<?php echo e(asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>



<div wire:ignore.self>


    <?php if($currentPage == PAGECREATEFORM): ?>
        <?php echo $__env->make('livewire.utilisateurs.create', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if($currentPage == PAGEEDITFORM): ?>
        <?php echo $__env->make('livewire.utilisateurs.edit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if($currentPage == PAGELIST): ?>
        <?php echo $__env->make('livewire.utilisateurs.liste', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

</div>


<script>
    // Success messages
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });

    // Error messages
    window.addEventListener("showErrorMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-times me-1',
            message: event.detail.message || 'Une erreur est survenue!'
        });
    });

    // Confirmation dialogs
    window.addEventListener("showConfirmMessage", event => {
        Swal.fire({
            title: event.detail.message.title,
            html: event.detail.message.text,
            icon: event.detail.message.type,
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: event.detail.message.confirmButtonText || 'Confirmer',
            cancelButtonText: event.detail.message.cancelButtonText || 'Annuler',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.emit('confirmDelete', event.detail.message.data.user_id);
            }
        });
    });

    // Date picker initialization
    window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    });

    // Form validation enhancement
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-focus first input with error
        const firstError = document.querySelector('.is-invalid');
        if (firstError) {
            firstError.focus();
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Enhanced form interactions
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    setTimeout(() => {
                        submitBtn.disabled = false;
                    }, 3000);
                }
            });
        });

        // Tooltip initialization
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // Loading states for Livewire
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.sent', () => {
            document.body.classList.add('loading');
        });

        Livewire.hook('message.processed', () => {
            document.body.classList.remove('loading');
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N for new user
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const newUserBtn = document.querySelector('[wire\\:click="goToAddUser()"]');
            if (newUserBtn) {
                newUserBtn.click();
            }
        }

        // Escape to go back
        if (e.key === 'Escape') {
            const backBtn = document.querySelector('[wire\\:click*="goToListUser"]');
            if (backBtn) {
                backBtn.click();
            }
        }
    });
</script><?php /**PATH C:\xampp\htdocs\ImsaaFoad\resources\views/livewire/utilisateurs/index.blade.php ENDPATH**/ ?>