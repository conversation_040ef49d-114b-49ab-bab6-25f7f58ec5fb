<?php

namespace App\Http\Livewire;

use App\Mail\Inscription;
use App\Models\Mention;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Livewire\WithFileUploads;

class MultiStepRegister extends Component
{
    use WithFileUploads;
    public $newUser = [];
    public $niveau;
    public $parcour;
    public $parcours;
    public $cv;
    public $diplome;
    public $relevebacc;
    public $releve1;
    public $releve2;
    public $releve3;
    public $releve4;
    public $terms;
    public $iteration;
    public $currentStep = 'parcours';
    public $previewDocument = null;
    

    // protected $listeners = ["selectDate" => 'getSelectedDate', "selectDateCin" => 'getSelectedDateCin'];


    public function mount()
    {
        $this->parcours = collect();
        
        // Récupérer les données sauvegardées si elles existent
        if (session()->has('registration_data')) {
            $savedData = session('registration_data');
            $this->newUser = $savedData['newUser'] ?? [];
            $this->niveau = $savedData['niveau'] ?? null;
            $this->parcour = $savedData['parcour'] ?? null;
            $this->currentStep = $savedData['currentStep'] ?? 'parcours';
            
            // Recharger les parcours si le niveau est défini
            if ($this->niveau) {
                $this->parcours = Mention::whereRelation('niveau', 'niveau_id', '=', $this->niveau)->get();
            }
        }
    }


    public function render()
    {
        return view('livewire.multi-step-register');
    }

    public function updatedNiveau($value)
    {
        $this->parcours = Mention::whereRelation('niveau', 'niveau_id', '=', $value)->get();
        $this->parcour = $this->parcours->first()->id ?? null;
        $this->relevebacc = null;
        $this->cv = null;
        $this->diplome = null;
        $this->releve1 = null;
        $this->releve2 = null;
        $this->releve3 = null;
        $this->releve4 = null;
        $this->iteration++;
    }

    // public function getSelectedDate($date)
    // {
    //     $this->newUser["date_naissance"] = $date;
    // }

    // public function getSelectedDateCin($date)
    // {
    //     $this->newUser["date_delivrance"] = $date;
    // }

    public function getProgressPercentage()
    {
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $completedSteps = 0;

        foreach ($steps as $step) {
            if ($this->isStepComplete($step)) {
                $completedSteps++;
            }
        }

        return ($completedSteps / count($steps)) * 100;
    }

    public function isStepComplete($step)
    {
        switch ($step) {
            case 'parcours':
                return !empty($this->niveau) && !empty($this->parcour);
            
            case 'informations':
                $requiredFields = [
                    'nom', 'prenom', 'email', 'telephone1', 'sexe', 
                    'date_naissance', 'lieu_naissance', 'nationalite', 'adresse'
                ];
                
                foreach ($requiredFields as $field) {
                    if (empty($this->newUser[$field])) {
                        return false;
                    }
                }
                
                // Validation spécifique pour l'email
                if (!filter_var($this->newUser['email'], FILTER_VALIDATE_EMAIL)) {
                    return false;
                }
                
                // Validation spécifique pour le téléphone
                if (!preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $this->newUser['telephone1'])) {
                    return false;
                }
                
                return true;
            
            case 'documents':
                if ($this->niveau == 1) {
                    return !empty($this->relevebacc);
                } elseif ($this->niveau == 2) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve1);
                } elseif ($this->niveau == 3) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve2);
                } elseif ($this->niveau == 4) {
                    return !empty($this->cv) && !empty($this->diplome);
                } elseif ($this->niveau == 5) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve4);
                }
                return false;
            
            case 'paiement':
                $requiredFields = ['reference', 'telenvoi', 'montantenvoi'];
                foreach ($requiredFields as $field) {
                    if (empty($this->newUser[$field])) {
                        return false;
                    }
                }
                return true;
            
            default:
                return false;
        }
    }

    protected function getFileValidationRules()
    {
        return [
            'max:5120', // 5MB
            'mimes:pdf,jpg,jpeg,png',
        ];
    }

    public function rules()
    {
        $fileRules = $this->getFileValidationRules();
        
        if ($this->niveau == 1) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'relevebacc' => array_merge(['required'], $fileRules),
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.telenvoi' => 'required',
                'newUser.montantenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 2) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => array_merge(['required'], $fileRules),
                'diplome' => array_merge(['required'], $fileRules),
                'releve1' => array_merge(['required'], $fileRules),
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.telenvoi' => 'required',
                'newUser.montantenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 3) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                'releve2' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 4) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                // 'releve1' => 'required',
                // 'releve2' => 'required',
                'releve3' => '',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 5) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                // 'releve1' => 'required',
                // 'releve2' => 'required',
                'releve4' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } else {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        }
    }

    protected function getCustomMessages()
    {
        return [
            'relevebacc.max' => 'Le fichier ne doit pas dépasser 5MB',
            'relevebacc.mimes' => 'Le fichier doit être au format PDF, JPG, JPEG ou PNG',
            'cv.max' => 'Le CV ne doit pas dépasser 5MB',
            'cv.mimes' => 'Le CV doit être au format PDF, JPG, JPEG ou PNG',
            'diplome.max' => 'Le diplôme ne doit pas dépasser 5MB',
            'diplome.mimes' => 'Le diplôme doit être au format PDF, JPG, JPEG ou PNG',
            'releve1.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve1.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve2.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve2.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve3.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve3.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve4.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve4.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
        ];
    }

    public function validate($rules = null, $messages = [], $attributes = [])
    {
        return parent::validate($rules, array_merge($this->getCustomMessages(), $messages), $attributes);
    }

    public function register()
    {
        $this->emit('showLoading');
        $validationAttributes = $this->validate();
        
        // Nettoyer les données sauvegardées après l'inscription réussie
        session()->forget('registration_data');
        
        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";
        $validationAttributes["newUser"]["mention_id"] = $this->parcour;
        $validationAttributes["newUser"]["niveau_id"] = $this->niveau;

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur


        if ($this->niveau == 1) {
            if ($validationAttributes["relevebacc"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["relevebacc"])->toMediaCollection('Releve_ou_Diplome_Bacc', 'private');
        } elseif ($this->niveau == 2) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve1"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve1"])->toMediaCollection('Releve_L1_ou_Attestation', 'private');
        } elseif ($this->niveau == 3) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve2"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve2"])->toMediaCollection('Releve_L2', 'private');
        } elseif ($this->niveau == 4) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            if ($validationAttributes["releve3"] != null) {
                $user->addMedia($validationAttributes["releve3"])->toMediaCollection('Releve_L3_ou_Attestation', 'private');
            }
        } elseif ($this->niveau == 5) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve4"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve4"])->toMediaCollection('Releve_L4', 'private');
        }

        Mail::to($this->newUser["email"])->send(new Inscription($this->newUser["nom"], $this->newUser["prenom"]));

        $data = ['name' => $this->newUser["nom"] . ' ' . $this->newUser["prenom"], 'email' => $this->newUser["email"]];
        $this->newUser = [];
        $this->emit('hideLoading');
        return redirect()->route('registration.success', $data);
    }

    public function removeFile($property)
    {
        if ($this->$property) {
            $this->$property = null;
            $this->iteration++;
        }
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
        
        if (strpos($propertyName, 'newUser.') === 0) {
            $this->validateField($propertyName);
        }
        
        $this->saveToSession();
    }

    protected function saveToSession()
    {
        session()->put('registration_data', [
            'newUser' => $this->newUser,
            'niveau' => $this->niveau,
            'parcour' => $this->parcour,
            'currentStep' => $this->currentStep
        ]);
    }

    public function goToStep($step)
    {
        if ($this->canAccessStep($step)) {
            $this->currentStep = $step;
            $this->resetFileInputs();
            $this->iteration++;
            $this->saveToSession();
        }
    }

    public function previousStep()
    {
        $this->emit('showLoading');
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);
        
        if ($currentIndex > 0) {
            $this->currentStep = $steps[$currentIndex - 1];
            $this->resetFileInputs();
            $this->iteration++;
            $this->saveToSession();
        }
        $this->emit('hideLoading');
    }

    public function nextStep()
    {
        if (!$this->canProceedToNextStep()) {
            $this->addError('step', 'Veuillez remplir tous les champs obligatoires avant de continuer.');
            return;
        }

        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);
        
        if ($currentIndex < count($steps) - 1) {
            $this->currentStep = $steps[$currentIndex + 1];
            $this->resetFileInputs();
            $this->iteration++;
            $this->saveToSession();
        }
    }

    public function canAccessStep($step)
    {
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);
        $targetIndex = array_search($step, $steps);

        if ($targetIndex <= $currentIndex) {
            return true;
        }

        return $this->isStepComplete($this->currentStep);
    }

    public function canProceedToNextStep()
    {
        if (!$this->isStepComplete($this->currentStep)) {
            return false;
        }

        // Validation supplémentaire selon l'étape
        switch ($this->currentStep) {
            case 'parcours':
                return !empty($this->niveau) && !empty($this->parcour);
            
            case 'informations':
                return !empty($this->newUser['nom']) && 
                       !empty($this->newUser['prenom']) && 
                       !empty($this->newUser['email']) && 
                       !empty($this->newUser['telephone1']) &&
                       !empty($this->newUser['sexe']) &&
                       !empty($this->newUser['date_naissance']) &&
                       !empty($this->newUser['lieu_naissance']) &&
                       !empty($this->newUser['nationalite']) &&
                       !empty($this->newUser['adresse']);
            
            case 'documents':
                if ($this->niveau == 1) {
                    return !empty($this->relevebacc);
                } elseif ($this->niveau == 2) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve1);
                } elseif ($this->niveau == 3) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve2);
                } elseif ($this->niveau == 4) {
                    return !empty($this->cv) && !empty($this->diplome);
                } elseif ($this->niveau == 5) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve4);
                }
                return false;
            
            case 'paiement':
                return !empty($this->newUser['reference']) && 
                       !empty($this->newUser['telenvoi']) && 
                       !empty($this->newUser['montantenvoi']);
            
            default:
                return false;
        }
    }

    public function previewDocument($property)
    {
        if ($this->$property) {
            $this->previewDocument = $this->$property;
            $this->dispatchBrowserEvent('showDocumentPreview');
        }
    }

    protected function validateField($propertyName)
    {
        $field = str_replace('newUser.', '', $propertyName);
        $value = $this->newUser[$field] ?? null;

        switch ($field) {
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError('newUser.' . $field, 'Format d\'email invalide');
                }
                break;
            case 'telephone1':
            case 'telephone2':
                if ($value && !preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $value)) {
                    $this->addError('newUser.' . $field, 'Format de téléphone invalide (ex: +261 XX XX XXX XX)');
                }
                break;
            case 'date_naissance':
                if ($value) {
                    $date = \Carbon\Carbon::parse($value);
                    if ($date->age < 16) {
                        $this->addError('newUser.' . $field, 'Vous devez avoir au moins 16 ans');
                    }
                }
                break;
        }
    }

    private function resetFileInputs()
    {
        $this->cv = null;
        $this->diplome = null;
        $this->relevebacc = null;
        $this->releve1 = null;
        $this->releve2 = null;
        $this->releve3 = null;
        $this->releve4 = null;
    }
}
