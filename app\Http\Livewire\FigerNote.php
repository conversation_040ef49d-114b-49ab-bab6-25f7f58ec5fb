<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use Livewire\Component;

class FigerNote extends Component
{
    public $currentPage = PAGELIST;

    public $newFiger = [];



    public Parcour $current_parcours;
    public Parcour $current_parcours1;
    public Parcour $current_parcours2;
    public $parcour1;
    public $parcour2;
    public Niveau $current_niveau;
    public Semestre $current_semestre;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.administration.figer.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editSemestre.nom' => 'required',
            ];
        }

        return [
            'newFiger.niveau_id' => 'required',
            'newFiger.semestre_id' => 'required',
            'newFiger.annee_universitaire_id' => 'required',

        ];
    }
    
    public function figer(){
        $validationAttributes = $this->validate();

        $history = HistoryNote::query()
        ->whereHas('ue', fn ($q) => $q->whereNiveauId($validationAttributes["newFiger"]["niveau_id"])->whereSemestreId($validationAttributes["newFiger"]["semestre_id"])->whereAnneeUniversitaireId($validationAttributes["newFiger"]["annee_universitaire_id"]));
        
        $matiere = Matiere::query()
        ->whereHas('ue', fn ($q) => $q->whereNiveauId($validationAttributes["newFiger"]["niveau_id"])->whereSemestreId($validationAttributes["newFiger"]["semestre_id"])->whereAnneeUniversitaireId($validationAttributes["newFiger"]["annee_universitaire_id"]));

        $history->update([
            'is_valid' => 1,
        ]);

        $matiere->update([
            'is_filled' => 1,
        ]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Note figée avec succès!"]);
    }
}
