{"__meta": {"id": "Xee41ef2a00275cd9826c2ba46697bc3e", "datetime": "2025-07-01 16:50:47", "utime": 1751377847.84967, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751377847.0881, "end": 1751377847.849693, "duration": 0.7615931034088135, "duration_str": "762ms", "measures": [{"label": "Booting", "start": 1751377847.0881, "relative_start": 0, "end": 1751377847.555499, "relative_end": 1751377847.555499, "duration": 0.46739912033081055, "duration_str": "467ms", "params": [], "collector": null}, {"label": "Application", "start": 1751377847.556594, "relative_start": 0.4684939384460449, "end": 1751377847.849696, "relative_end": 2.86102294921875e-06, "duration": 0.2931020259857178, "duration_str": "293ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23299480, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NY6g1Xuzra76ir4mHb22TH2V29cTs5zb8ToxXk4a", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2120622539 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2120622539\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1914185127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1914185127\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-844438791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-844438791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1588494310 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFnNGFFOHRSN0pSSG9lMzJDUHlnR0E9PSIsInZhbHVlIjoiM3BjVGpISmpETFREVXI0a3hMMTN0NU9Ld2J4cndrVnlqbWQ5T3lxdnRBSmhvWmF4QXQ3eEFyRENFVHJEL29GMXEwUG9pQ0RjYThWbkhVMVhNcUlJQndRaTV3UnhHcEZZMVlGU2VtMktkVGlSTEgyWG5PQ1Zjd2VvcE1OSWpYVWIiLCJtYWMiOiIxNGE5NDE2NDZiZjU0NWNhNzJkODVmNjA4MjRjNWE1MDZiZmUwYTZjMTQ2OTFkZmRkOWYxODg3ZjBmNjlhYzI4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjJURGs4L1M2cWFnRjkzdXNEYnVRc2c9PSIsInZhbHVlIjoiL051UjhJNWtYOC9hNHo3VUlZNmpjdi80emxrT3R1YjdkTzV1US9qaksrYUpSK0NBSWIwRGJpbmdHQ2RPQzl0RktiT04vWERYdFFGZmJ2NEh0K0VNazM0Q1dNdmlyYkRFUklnWjk3MzNSL1dacFBpZTFoQmJvUFpJU3hJa1VrTTUiLCJtYWMiOiJmM2MxNjY4MGZkNzI1Njg0OTNjNWMzNzg5YzY0Mjk1Y2ExNWQ0NWFmNTczNjdlMGNiZTZjMmYzMTU4MDEzZjE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588494310\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-229549424 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55384</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFnNGFFOHRSN0pSSG9lMzJDUHlnR0E9PSIsInZhbHVlIjoiM3BjVGpISmpETFREVXI0a3hMMTN0NU9Ld2J4cndrVnlqbWQ5T3lxdnRBSmhvWmF4QXQ3eEFyRENFVHJEL29GMXEwUG9pQ0RjYThWbkhVMVhNcUlJQndRaTV3UnhHcEZZMVlGU2VtMktkVGlSTEgyWG5PQ1Zjd2VvcE1OSWpYVWIiLCJtYWMiOiIxNGE5NDE2NDZiZjU0NWNhNzJkODVmNjA4MjRjNWE1MDZiZmUwYTZjMTQ2OTFkZmRkOWYxODg3ZjBmNjlhYzI4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjJURGs4L1M2cWFnRjkzdXNEYnVRc2c9PSIsInZhbHVlIjoiL051UjhJNWtYOC9hNHo3VUlZNmpjdi80emxrT3R1YjdkTzV1US9qaksrYUpSK0NBSWIwRGJpbmdHQ2RPQzl0RktiT04vWERYdFFGZmJ2NEh0K0VNazM0Q1dNdmlyYkRFUklnWjk3MzNSL1dacFBpZTFoQmJvUFpJU3hJa1VrTTUiLCJtYWMiOiJmM2MxNjY4MGZkNzI1Njg0OTNjNWMzNzg5YzY0Mjk1Y2ExNWQ0NWFmNTczNjdlMGNiZTZjMmYzMTU4MDEzZjE3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751377847.0881</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751377847</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229549424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1354316811 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NY6g1Xuzra76ir4mHb22TH2V29cTs5zb8ToxXk4a</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">X3Sg16eycrQWO1JJCamLafPlO6rd1mlMpGA7VgM5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354316811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-933712932 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 13:50:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRxOExMb1pLbDMyS3dQdnZaL2pwQ1E9PSIsInZhbHVlIjoieEVPZmFMVWVWSzZOd0gwRC8yZUg1VmxrM0lvY2JHWEF0MG9lYXZKclF4bFAzbGJkMGg0c2VOcGs0Q0FraW4wREM0MlQ5NkR4dHV1c1c4R1AvWGpFbXRiMThScWlpY1dCUlpvcENFSStpVGlTb2FYc3hJZ3p1RkpKRVFpcmgyRTgiLCJtYWMiOiJhNTZmZDI1ZTFiMDMwOWRlMTQ0YzhmYmZkMDYxZTEyNmI2N2IwOGY1NmE3MDM2Y2NlOTkwNjM2MGU5MjhjM2VhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 15:50:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IkxadXVoN1o4ZnFVd25pYkhCU0o4RWc9PSIsInZhbHVlIjoiQ1B5TEQ3UXpIdG5OTGFVNzluSzZuWURuM2FUTWhwWGx2N0J1VFlHR1lQQUxreUc4NGIyVHFKVk4waE1lSmxKc3Mxa2JJQUtaTUZTSGhLTFVBMzgxbmxCZU50MGs2MnB0YU5EWnhHUWRZVU04RmFkYWtWNXpnbyszSHFyUHg2dmUiLCJtYWMiOiI1YWQxZGI4ZGViZGU1YWFmNWY4ODM3ODA4Y2IwZmEzOWU5MWU5OTVkNmVkNzhiYzM4MGRiYTljMzU0MTY2ODhmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 15:50:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRxOExMb1pLbDMyS3dQdnZaL2pwQ1E9PSIsInZhbHVlIjoieEVPZmFMVWVWSzZOd0gwRC8yZUg1VmxrM0lvY2JHWEF0MG9lYXZKclF4bFAzbGJkMGg0c2VOcGs0Q0FraW4wREM0MlQ5NkR4dHV1c1c4R1AvWGpFbXRiMThScWlpY1dCUlpvcENFSStpVGlTb2FYc3hJZ3p1RkpKRVFpcmgyRTgiLCJtYWMiOiJhNTZmZDI1ZTFiMDMwOWRlMTQ0YzhmYmZkMDYxZTEyNmI2N2IwOGY1NmE3MDM2Y2NlOTkwNjM2MGU5MjhjM2VhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 15:50:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IkxadXVoN1o4ZnFVd25pYkhCU0o4RWc9PSIsInZhbHVlIjoiQ1B5TEQ3UXpIdG5OTGFVNzluSzZuWURuM2FUTWhwWGx2N0J1VFlHR1lQQUxreUc4NGIyVHFKVk4waE1lSmxKc3Mxa2JJQUtaTUZTSGhLTFVBMzgxbmxCZU50MGs2MnB0YU5EWnhHUWRZVU04RmFkYWtWNXpnbyszSHFyUHg2dmUiLCJtYWMiOiI1YWQxZGI4ZGViZGU1YWFmNWY4ODM3ODA4Y2IwZmEzOWU5MWU5OTVkNmVkNzhiYzM4MGRiYTljMzU0MTY2ODhmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 15:50:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933712932\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-494080773 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NY6g1Xuzra76ir4mHb22TH2V29cTs5zb8ToxXk4a</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494080773\", {\"maxDepth\":0})</script>\n"}}