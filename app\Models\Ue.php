<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ue extends Model
{
    use HasFactory, SoftDeletes;

    public $timestamps = false;

    protected $fillable = [
        'nom',
        'code',
        'credit',
        'parcour_id',
        'niveau_id',
        'semestre_id',
        'annee_universitaire_id',
        
    ];

    public function matiere(){
        return $this->hasMany(Matiere::class);
    }

    public function semestre(){
        return $this->belongsTo(Semestre::class);
    }

    public function niveau(){
        return $this->belongsTo(Niveau::class);
    }

    public function annee(){
        return $this->belongsTo(AnneeUniversitaire::class, "annee_universitaire_id", "id");
    }

    public function parcours(){
        return $this->belongsTo(Parcour::class, "parcour_id", "id");
    }

    public function note(){
        return $this->hasManyThrough(Note::class, Matiere::class);
    }

    
}
