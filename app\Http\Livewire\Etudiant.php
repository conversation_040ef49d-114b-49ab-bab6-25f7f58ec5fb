<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;

class Etudiant extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newUser = [];
    public $editUser = [];
    public $query;
    public $notes;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreAnnee;

    protected $listeners = ["selectDate" => 'getSelectedDate'];

    public function updatingQuery()
    {
        $this->resetPage();
    }
    public function updatingFiltreAnnee(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }

    public function render()
    {
        Carbon::setLocale("fr");

        $persQuery = InscriptionStudent::query()->with(['user', 'parcours', 'niveau', 'annee']);

        if ($this->query != "") {
            $persQuery->whereHas('user', function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('prenom', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreParcours != "") {
            $persQuery->whereParcourId($this->filtreParcours);
        }
        if ($this->filtreNiveau != "") {
            $persQuery->whereNiveauId($this->filtreNiveau);
        }
        if ($this->filtreAnnee != "") {
            $persQuery->whereAnneeUniversitaireId($this->filtreAnnee);
        }

        return view('livewire.deraq.etudiant.index', [
            "etus" => $persQuery->whereRelation('user.roles', 'role_id', '=', 5)
                ->paginate(10),
            "parcours" => Parcour::all(),
            "annees" => AnneeUniversitaire::all(),
            "niveaux" => Niveau::all(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUser.nom' => 'required',
                'editUser.prenom' => 'required',
                // 'editUser.email' => ['email', Rule::unique("users", "email")->ignore($this->editUser['id'])],
                'editUser.telephone1' => ['required', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])],

                'editUser.sexe' => 'required',
                'editUser.niveau_id' => 'required',
                'editUser.parcour_id' => 'required',
            ];
        }

        return [
            'newUser.nom' => 'required',
            'newUser.prenom' => 'required',
            // 'newUser.email' => 'email|unique:users,email',
            'newUser.sexe' => 'required',
            'newUser.telephone1' => 'numeric|unique:users,telephone1',
            'newUser.niveau_id' => 'required',
            'newUser.parcour_id' => 'required',
        ];
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        // return redirect(request()->header('Referer'));
    }

    public function goToAddUser()
    {
        $this->currentPage = PAGECREATEFORM;
        $this->dispatchBrowserEvent("helperDatePicker");
    }

    public function getSelectedDate($date)
    {

        $this->newUser["date_naissance"] = $date;
    }

    public function goToEditUser($id)
    {
        $this->editUser = User::find($id)->toArray();
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
    }

    public function addUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        $user = User::create($validationAttributes["newUser"]);

        $user->roles()->attach(5);

        InscriptionStudent::create([
            "user_id" => $user->id,
            "annee_universitaire_id" => 4,
            "niveau_id" => $user->niveau_id,
            "parcour_id" => $user->parcour_id,
        ]);

        $this->newUser = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Etudiant créé avec succès!"]);
        // dump($pass);
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        User::find($this->editUser["id"])->update($validationAttributes["editUser"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur mis à jour avec succès!"]);
    }

    public function confirmDelete($name, $id)
    {
        $this->dispatchBrowserEvent("showConfirmMessage", ["message" => [
            "text" => "Vous êtes sur le point de supprimer $name de la liste des utilisateurs. Voulez-vous continuer?",
            "title" => "Êtes-vous sûr de continuer?",
            "type" => "warning",
            "data" => [
                "user_id" => $id
            ]
        ]]);
    }

    public function deleteUser($id)
    {
        User::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur supprimé avec succès!"]);
    }

    public function voirNote($id)
    {
        $this->currentPage = PAGEOTHERFORM;
        $this->notes = Note::with(['user', 'historyNote', 'matiere'])->whereUserId($id)->paginate(10);
    }
}
