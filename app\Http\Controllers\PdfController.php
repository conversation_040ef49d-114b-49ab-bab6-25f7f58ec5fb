<?php

namespace App\Http\Controllers;

use App\Models\HistoriquePayment;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class PdfController extends Controller
{
    public function generatePdf() {
       $pays = HistoriquePayment::with(['user', 'payment', 'moyen', 'encaissement'])->whereDate('created_at', now()->today())->get();

       $pdf = Pdf::loadView('pdf.payment', compact('pays'));
       return $pdf->download("payment ". now() . ".pdf");
    }

    public function downloadPj($id, $collection){
        $user = User::findOrFail($id);
        $media = $user->getFirstMedia($collection);

        return $media;
    }

    public function downloadPdf(){
        $path = public_path("pdf/lettre_engagement_ IMSAA.pdf");

        return response()->download($path);
    }
}
