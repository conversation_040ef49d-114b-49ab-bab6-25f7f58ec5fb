<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;

class ListeNewEtu extends Component
{
    public $currentPage = PAGELIST;

    public $newEtus = [];

    public $etus = [];


    public Parcour $current_parcours;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.administration.contactEtu.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required',
            'newEtus.parcour_id' => 'required',
            'newEtus.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->newEtus = [];
        $this->etus = [];
    }

    public function goToEtat()
    {

        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["newEtus"]["parcour_id"], $validationAttributes["newEtus"]["niveau_id"], $validationAttributes["newEtus"]["annee_universitaire_id"]);

        $this->currentPage = PAGECREATEFORM;
    }

    public function generate($parcour_id, $niveau_id, $annee_universitaire_id)
    {
        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);



        if ($parcour_id == 100) {
            $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))
                ->get();
        } else {
            $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id)->whereParcourId($parcour_id))
                ->get();
        }
    }
}
