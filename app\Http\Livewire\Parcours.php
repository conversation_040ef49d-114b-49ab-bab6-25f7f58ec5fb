<?php

namespace App\Http\Livewire;

use App\Models\Parcour;
use App\Models\Domaine;
use App\Models\Mention;
use Livewire\Component;

class Parcours extends Component
{
    public $currentPage = PAGELIST;

    public $newParcours = [];
    public $editParcours = [];
    public $current_mention;

    public function mount($mentionId)
    {
        $this->current_mention = Mention::find($mentionId);
        // $this->parcours = Parcour::all(['id', 'sigle']);
        // $this->ues = collect();
    }

    public function render()
    {
        return view('livewire.deraq.parcours.index', [
            "parcours" => Parcour::whereHas('mention', fn ($q) => $q->whereMentionId($this->current_mention->id))->get(),
            "mentions" => Mention::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editParcours.nom' => 'required',
                'editParcours.sigle' => 'required',
                'editParcours.mention_id' => 'required',
            ];
        }

        return [
            'newParcours.nom' => 'required',
            'newParcours.sigle' => 'required',

        ];
    }

    public function goToListParcours()
    {
        $this->currentPage = PAGELIST;
        $this->newParcours = [];
    }

    public function goToAddParcours()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditParcours(Parcour $cours){
        $this->editParcours = $cours->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addParcours()
    {

        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        Parcour::create([
            "nom" => $validationAttributes["newParcours"]["nom"],
            "sigle" => $validationAttributes["newParcours"]["sigle"],
            "mention_id" => $this->current_mention->id,
        ]);

        $this->newParcours = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Parcours créé avec succès!"]);
        // dump($pass);
        $this->goToListParcours();
    }

    public function updateParcours(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        Parcour::find($this->editParcours["id"])->update([
            "nom" => $validationAttributes["editParcours"]["nom"],
            "sigle" => $validationAttributes["editParcours"]["sigle"],
            "mention_id" => $validationAttributes["editParcours"]["mention_id"],
        ]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Parcours mis à jour avec succès!"]);
        $this->editParcours = [];
        $this->goToListParcours();
    }

    public function deleteParcours($id)
    {
        Parcour::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Parcours supprimé avec succès!"]);
    }
}
