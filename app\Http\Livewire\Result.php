<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;
use Illuminate\Support\Facades\View;

class Result extends Component
{
    public $currentPage = PAGELIST;

    public $newResults = [];

    public $notes = [];


    public Parcour $current_parcours;
    public Parcour $current_parcours1;
    public Parcour $current_parcours2;
    public $parcour1;
    public $parcour2;
    public Niveau $current_niveau;
    public Semestre $current_semestre;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.deraq.resultat.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editSemestre.nom' => 'required',
            ];
        }

        return [
            'newResults.niveau_id' => 'required',
            'newResults.parcour_id' => 'required',
            'newResults.semestre_id' => 'required',
            'newResults.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->newResults = [];
        $this->notes = [];
        $this->parcour1 = 0;
        $this->parcour2 = 0;
    }

    public function goToAddResult()
    {

        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["newResults"]["parcour_id"], $this->parcour1, $this->parcour2, $validationAttributes["newResults"]["niveau_id"], $validationAttributes["newResults"]["semestre_id"], $validationAttributes["newResults"]["annee_universitaire_id"]);

        $this->currentPage = PAGECREATEFORM;
        // foreach (Matiere::whereHas('ue', fn ($q) => $q->whereSemestreId($validationAttributes["newResults"]["semestre_id"])->whereAnneeUniversitaireId($validationAttributes["newResults"]["annee_universitaire_id"])->whereParcourId($validationAttributes["newResults"]["parcour_id"])->whereNiveauId($validationAttributes["newResults"]["niveau_id"])->get()) as $matiere) {
        //     if($matiere->)
        // }


    }

    public function generate($parcour_id, $parcour_id1, $parcour_id2, $niveau_id, $semestre_id, $annee_universitaire_id)
    {
        // $parcours = Parcour::find($parcour_id);
        // $parcours1 = Parcour::find($parcour_id1);
        // $niveau = Niveau::find($niveau_id);
        // $semestre = Semestre::find($semestre_id);
        // $this->currentPage = PAGECREATEFORM;
        // $notes = Note::with(['user', 'ue'])->whereHas('user', fn ($q) => $q->whereParcourId($parcour_id)->whereNiveauId($niveau_id))
        //     ->whereHas('ue', fn ($q) => $q->whereSemestreId($semestre_id))->get();

        $this->current_parcours = Parcour::find($parcour_id);
        if ($parcour_id1 != 0) {
            $this->current_parcours1 = Parcour::find($parcour_id1);
        }
        if ($parcour_id2 != 0) {
            $this->current_parcours2 = Parcour::find($parcour_id2);
        }

        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_semestre = Semestre::find($semestre_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);


        if ($this->parcour1 == 0 && $this->parcour2 == 0) {
            $users = User::whereHas('info', fn ($q) => $q->whereParcourId($parcour_id)
                ->whereNiveauId($niveau_id)
                ->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom']);

            $this->calcul($users, $semestre_id, $annee_universitaire_id);
            // dd($this->notes);
        } else if ($this->parcour1 != 0 && $this->parcour2 == 0) {
            $users = User::whereHas('info', fn ($q) => $q->where(function ($query) {
                $query->where('parcour_id', $this->current_parcours->id)
                    ->orWhere('parcour_id', $this->parcour1);
            })
                ->whereNiveauId($niveau_id)
                ->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom']);

            $this->calcul($users, $semestre_id, $annee_universitaire_id);
            // dd($this->notes);
        } else if ($this->parcour1 != 0 && $this->parcour2 != 0) {
            $users = User::whereHas('info', fn ($q) => $q->where(function ($query) {
                $query->where('parcour_id', $this->current_parcours->id)
                    ->orWhere('parcour_id', $this->parcour1)
                    ->orWhere('parcour_id', $this->parcour2);
            })
                ->whereNiveauId($niveau_id)
                ->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom']);

            $this->calcul($users, $semestre_id, $annee_universitaire_id);
            // dd($this->notes);
        }
    }

    public function calcul(Collection $users, $semestre_id, $annee_universitaire_id)
    {
        foreach ($users as $user) {
            $moy = 0;
            $ue = Ue::query()->with(['matiere'])
                ->whereSemestreId($semestre_id)
                ->whereAnneeUniversitaireId($annee_universitaire_id)
                ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($user->id))
                ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($user->id))
                ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($user->id))
                ->get();

            foreach ($ue as $ec) {
                $mat = [];
                $total = 0;
                for ($i = 0; $i < $ec->matiere->count(); $i++) {
                    $total += $ec->matiere[$i]->moyenne;
                    array_push($mat, ["nom" => $ec->matiere[$i]->nom, "note" => $ec->matiere[$i]->moyenne]);
                }

                $moy += $total / $ec->matiere->count();
            }

            // if ($moy < 10 || containsValueLessThanFive($mat)) {
            //     array_push($info, ["nom" => $ec->nom, "moy" => $moy, "matiere" => $mat]);
            // }
            if ($moy > 16) {
                $mention = "Très bien";
            } elseif ($moy > 14) {
                $mention = "Bien";
            } elseif ($moy > 12) {
                $mention = "Assez-bien";
            } else {
                $mention = "Passable";
            }

            array_push($this->notes, ["nom" => $user->nom, "prenom" => $user->prenom, "moy" => $moy, "mention" => $mention]);
            array_multisort(array_column($this->notes, 'moy'), SORT_DESC, $this->notes);
            // dd($info);
            // dd($collection);

        }
    }

    public function pdfGenerate()
    {

        // $pdfContent = PDF::loadView('pdf.resultat', compact('results'))->output();
        // return response()->streamDownload(
        //     fn () => print($pdfContent),
        //     "filename.pdf"
        // );

        if ($this->parcour1 != 0 && $this->parcour2 == 0) {
            $view     = View::make('pdf.resultat', ['notes' => $this->notes, 'current_parcours' => $this->current_parcours, 'current_parcours1' => $this->current_parcours1, 'current_niveau' => $this->current_niveau, 'current_semestre' => $this->current_semestre, 'parcour1' => $this->parcour1, 'parcour2' => $this->parcour2, 'current_annee' => $this->current_annee]);
            $html     = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');
            // dd($view);
            $pdfContent = PDF::loadHtml($html)->output();
            return response()->streamDownload(
                fn () => print($pdfContent),
                "resultat" . $this->current_parcours->sigle . $this->current_parcours1->sigle . ".pdf"
            );
        } elseif ($this->parcour1 != 0 && $this->parcour2 != 0) {
            $view     = View::make('pdf.resultat', ['notes' => $this->notes, 'current_parcours' => $this->current_parcours, 'current_parcours1' => $this->current_parcours1, 'current_parcours2' => $this->current_parcours2, 'current_niveau' => $this->current_niveau, 'current_semestre' => $this->current_semestre, 'parcour1' => $this->parcour1, 'parcour2' => $this->parcour2, 'current_annee' => $this->current_annee]);
            $html     = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');
            // dd($view);
            $pdfContent = PDF::loadHtml($html)->output();
            return response()->streamDownload(
                fn () => print($pdfContent),
                "resultat" . $this->current_parcours->sigle . $this->current_parcours1->sigle . $this->current_parcours2->sigle  . ".pdf"
            );
        } else {
            $view     = View::make('pdf.resultat', ['notes' => $this->notes, 'current_parcours' => $this->current_parcours, 'current_niveau' => $this->current_niveau, 'current_semestre' => $this->current_semestre, 'parcour1' => $this->parcour1, 'parcour2' => $this->parcour2, 'current_annee' => $this->current_annee]);
            $html     = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');
            // dd($view);
            $pdfContent = PDF::loadHtml($html)->output();
            return response()->streamDownload(
                fn () => print($pdfContent),
                "resultat" . $this->current_parcours->sigle . ".pdf"
            );
        }
    }
}
