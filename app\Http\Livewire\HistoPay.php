<?php

namespace App\Http\Livewire;

use App\Models\HistoriquePayment;
use App\Models\MoyenPayment;
use App\Models\TypePayment;
use App\Models\User;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class HistoPay extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editPay = [];

    public User $current_user;

    public $startDate;
    public $endDate;
    public $query;
    public $filtre = false;

    protected $listeners = ["selectDateStart" => 'getSelectedDateStart', "selectDateEnd" => 'getSelectedDateEnd'];

    public function filter() {
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->filtre = !$this->filtre;
    }

    public function updatingQuery(){
        $this->resetPage();
    }
    
    public function render()
    {
        $q = HistoriquePayment::with(['user', 'payment', 'moyen', 'encaissement']);

        if($this->query != ""){
            $q->where('code', 'like', '%'. $this->query .'%')
                ->orWhereHas('user', function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('prenom', 'like', '%'. $this->query .'%');
            });               
        }

        if ($this->startDate != "" && $this->endDate != "") {
            $q->whereDate('created_at', '>=', $this->startDate)
            ->whereDate('created_at', '<=', $this->endDate);
        }

        return view('livewire.administration.histopay.index', [
            "pays" => $q->paginate(10),
            "moyens" => MoyenPayment::all(),
            "types" => TypePayment::all(),
            "totDebit" => HistoriquePayment::whereTypeEncaissementId(1)->sum('montant'),
            "totCredit" => HistoriquePayment::whereTypeEncaissementId(2)->sum('montant'),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function getSelectedDateStart($date)
    {
        $this->startDate = $date;
        $this->resetPage();
    }

    public function getSelectedDateEnd($date)
    {
        $this->endDate = $date;
        $this->resetPage();
    }

    public function valider(HistoriquePayment $historique) {
        $historique->update([
            "is_valid" => 1,
        ]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Validé avec succès!"]);
        $this->render();
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {
            return [
                'editPay.moyen_payment_id' => 'required',
                'editPay.type_payment_id' => 'required',
                'editPay.montant' => 'required',
                'editPay.created_at' => 'required',
                'editPay.code' => ['required', Rule::unique("historique_payments", "code")->ignore($this->editPay['id'])],
            ];
        }
    }

    public function goToEditPay($id)
    {
        $this->editPay = HistoriquePayment::find($id)->toArray();
        $this->current_user = User::find($this->editPay["user_id"]);
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
    }

    public function goToListPay()
    {
        $this->currentPage = PAGELIST;
        $this->editPay = [];
    }

    public function updatePay()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        HistoriquePayment::find($this->editPay["id"])->update($validationAttributes["editPay"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Historique de payment mis à jour avec succès!"]);

    }

    public function deletePay($id)
    {
        HistoriquePayment::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Paiment supprimé avec succès!"]);
    }
}
