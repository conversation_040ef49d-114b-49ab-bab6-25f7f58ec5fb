<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use Livewire\Component;
use Livewire\WithPagination;

class Ues extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newUe = [];
    public $editUe = [];
    public $query;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreAnnee;

    public function updatingQuery(){
        $this->resetPage();
    }
    public function updatingFiltreAnnee(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }

    public function render()
    {
        $persQuery = Ue::query();

        if($this->query != ""){
            $persQuery->where(function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreParcours != ""){
            $persQuery->whereParcourId($this->filtreParcours);
        }
        if($this->filtreNiveau != ""){
            $persQuery->whereNiveauId($this->filtreNiveau);
        }
        if($this->filtreAnnee != ""){
            $persQuery->whereAnneeUniversitaireId($this->filtreAnnee);
        }

        return view('livewire.deraq.ue.index', [
            "ues" => $persQuery->paginate(10),
            "parcours" => Parcour::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all(),
            "niveaux" => Niveau::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUe.nom' => 'required',
                'editUe.code' => 'required',
                'editUe.credit' => 'required',
                'editUe.parcour_id' => 'required',
                'editUe.semestre_id' => 'required',
                'editUe.niveau_id' => 'required',
                'editUe.annee_universitaire_id' => 'required',
            ];
        }

        return [
            'newUe.nom' => 'required',
            'newUe.code' => 'required',
            'newUe.credit' => 'required',
            'newUe.parcour_id' => 'required',
            'newUe.niveau_id' => 'required',
            'newUe.semestre_id' => 'required',
            'newUe.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListUe()
    {
        $this->currentPage = PAGELIST;
        $this->newUe = [];
    }

    public function goToAddUe()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditUe(Ue $ue){
        $this->editUe = $ue->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addUe()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        Ue::create($validationAttributes["newUe"]);

        $this->newUe = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Ue créé avec succès!"]);
        $this->goToListUe();
    }

    public function updateUe(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        Ue::find($this->editUe["id"])->update($validationAttributes["editUe"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Ue mis à jour avec succès!"]);
        $this->goToListUe();
    }

    public function deleteUe($id)
    {
        Ue::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Ue supprimé avec succès!"]);
    }
}
