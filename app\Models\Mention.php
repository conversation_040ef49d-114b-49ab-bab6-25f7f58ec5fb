<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Mention extends Model
{
    use HasFactory, SoftDeletes;

    public $timestamps = false;
    
    protected $fillable = [
        'nom',
        'domaine_id',
        
    ];

    public function domaine(){
        return $this->belongsTo(Domaine::class);
    }
    public function niveau(){
        return $this->belongsToMany(Niveau::class);
    }
}
