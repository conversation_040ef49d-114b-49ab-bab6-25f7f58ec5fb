{"__meta": {"id": "X887294d2d3c35417cedea2e376fd1dcf", "datetime": "2025-07-01 17:18:14", "utime": 1751379494.452333, "method": "GET", "uri": "/livewire/livewire.js?id=fe747446aa84856d8b66", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379493.737487, "end": 1751379494.452359, "duration": 0.714871883392334, "duration_str": "715ms", "measures": [{"label": "Booting", "start": 1751379493.737487, "relative_start": 0, "end": 1751379494.379528, "relative_end": 1751379494.379528, "duration": 0.6420409679412842, "duration_str": "642ms", "params": [], "collector": null}, {"label": "Application", "start": 1751379494.380303, "relative_start": 0.6428158283233643, "end": 1751379494.452362, "relative_end": 3.0994415283203125e-06, "duration": 0.07205915451049805, "duration_str": "72.06ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 22544472, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Controllers\\LivewireJavaScriptAssets@source", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\LivewireJavaScriptAssets.php&line=9\">\\vendor\\livewire\\livewire\\src\\Controllers\\LivewireJavaScriptAssets.php:9-12</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-1523758675 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1523758675\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1927412195 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">fe747446aa84856d8b66</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927412195\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1414426588 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1414426588\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1773741033 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IldzZXI4N0J3cHlYaVUycUpsVEFsRXc9PSIsInZhbHVlIjoiSktpOEhCOVNrYzh0bDJXdXRYUjhGTlZpMGJaVzlVdVJTMEp5M1FGdXhvaGNzdGZveVNJLzdMb1hrd2E4YmNZbGxXZkhrdW5VY1Y4L2ZqWklLZXIxaVp5cUx4RXRPWDRoWitKbXJGUldjYTBXRHB5WGkzU0todkFJMUtzb050bloiLCJtYWMiOiJkMWE2Y2I5NTFmNTU4M2E5OGJjYjZkNTJmYmRiN2ViMGE4NTYzNGI4M2MxNGFmZGIzY2RhN2M5ZjIxMmNjYmU1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkpTMGk1SUZJWkR5VXZjcmRRc2lUY0E9PSIsInZhbHVlIjoiNHZucHpxU0E0QnljeE1mS2RaS09sMzAzcnFmVDRxWGhpUUEvaDRWRC95blAvcW9KdHNsNlNpZ1lYQkxmSU1HUFVPdXhqeVQwNEFHOHQ1cGhpMzVzaEVPUSsvR1pXb2JQbEp4U1ZmdjczcWQ1aDRDY1VhcDJSTEljcS96R3FEOUEiLCJtYWMiOiIwYjY3NWZhYTkxNzRjZDViNmNlNWRlY2Y1Nzc4OGRkZTRmNjNhOWFlNzQ1MGMwMDg5NWY0NjgyNDQyMjY5ZGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773741033\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-753591318 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56667</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"45 characters\">/livewire/livewire.js?id=fe747446aa84856d8b66</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/livewire.js</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"108 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console/../resources/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/livewire.js</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">id=fe747446aa84856d8b66</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IldzZXI4N0J3cHlYaVUycUpsVEFsRXc9PSIsInZhbHVlIjoiSktpOEhCOVNrYzh0bDJXdXRYUjhGTlZpMGJaVzlVdVJTMEp5M1FGdXhvaGNzdGZveVNJLzdMb1hrd2E4YmNZbGxXZkhrdW5VY1Y4L2ZqWklLZXIxaVp5cUx4RXRPWDRoWitKbXJGUldjYTBXRHB5WGkzU0todkFJMUtzb050bloiLCJtYWMiOiJkMWE2Y2I5NTFmNTU4M2E5OGJjYjZkNTJmYmRiN2ViMGE4NTYzNGI4M2MxNGFmZGIzY2RhN2M5ZjIxMmNjYmU1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkpTMGk1SUZJWkR5VXZjcmRRc2lUY0E9PSIsInZhbHVlIjoiNHZucHpxU0E0QnljeE1mS2RaS09sMzAzcnFmVDRxWGhpUUEvaDRWRC95blAvcW9KdHNsNlNpZ1lYQkxmSU1HUFVPdXhqeVQwNEFHOHQ1cGhpMzVzaEVPUSsvR1pXb2JQbEp4U1ZmdjczcWQ1aDRDY1VhcDJSTEljcS96R3FEOUEiLCJtYWMiOiIwYjY3NWZhYTkxNzRjZDViNmNlNWRlY2Y1Nzc4OGRkZTRmNjNhOWFlNzQ1MGMwMDg5NWY0NjgyNDQyMjY5ZGE0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379493.7375</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379493</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753591318\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1436794345 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IldzZXI4N0J3cHlYaVUycUpsVEFsRXc9PSIsInZhbHVlIjoiSktpOEhCOVNrYzh0bDJXdXRYUjhGTlZpMGJaVzlVdVJTMEp5M1FGdXhvaGNzdGZveVNJLzdMb1hrd2E4YmNZbGxXZkhrdW5VY1Y4L2ZqWklLZXIxaVp5cUx4RXRPWDRoWitKbXJGUldjYTBXRHB5WGkzU0todkFJMUtzb050bloiLCJtYWMiOiJkMWE2Y2I5NTFmNTU4M2E5OGJjYjZkNTJmYmRiN2ViMGE4NTYzNGI4M2MxNGFmZGIzY2RhN2M5ZjIxMmNjYmU1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkpTMGk1SUZJWkR5VXZjcmRRc2lUY0E9PSIsInZhbHVlIjoiNHZucHpxU0E0QnljeE1mS2RaS09sMzAzcnFmVDRxWGhpUUEvaDRWRC95blAvcW9KdHNsNlNpZ1lYQkxmSU1HUFVPdXhqeVQwNEFHOHQ1cGhpMzVzaEVPUSsvR1pXb2JQbEp4U1ZmdjczcWQ1aDRDY1VhcDJSTEljcS96R3FEOUEiLCJtYWMiOiIwYjY3NWZhYTkxNzRjZDViNmNlNWRlY2Y1Nzc4OGRkZTRmNjNhOWFlNzQ1MGMwMDg5NWY0NjgyNDQyMjY5ZGE0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436794345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1672794105 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 14:18:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 29 Jan 2023 23:45:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:18:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">174545</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672794105\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-494827549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-494827549\", {\"maxDepth\":0})</script>\n"}}