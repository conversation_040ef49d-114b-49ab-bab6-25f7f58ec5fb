<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\TypePayment;
use App\Models\User;
use Livewire\Component;

class VoirEtat extends Component
{
    public $currentPage = PAGELIST;

    public $newEtus = [];

    public $etus = [];


    public Parcour $current_parcours;
    public $type1;
    public $type2;
    public Niveau $current_niveau;
    public TypePayment $current_type;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.administration.voiretat.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "types" => TypePayment::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required',
            'newEtus.etat' => 'required',
            'newEtus.parcour_id' => 'required',
            'newEtus.type_payment_id' => 'required',
            'newEtus.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->newEtus = [];
        $this->etus = [];
        $this->type1 = "";
        $this->type2 = "";
    }

    public function goToEtat()
    {

        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["newEtus"]["parcour_id"], $this->type1, $this->type2, $validationAttributes["newEtus"]["niveau_id"], $validationAttributes["newEtus"]["type_payment_id"], $validationAttributes["newEtus"]["annee_universitaire_id"], $validationAttributes["newEtus"]["etat"]);

        $this->currentPage = PAGECREATEFORM;
        // foreach (Matiere::whereHas('ue', fn ($q) => $q->whereTypePaymentId($validationAttributes["newEtus"]["type_payment_id"])->whereAnneeUniversitaireId($validationAttributes["newEtus"]["annee_universitaire_id"])->whereParcourId($validationAttributes["newEtus"]["parcour_id"])->whereNiveauId($validationAttributes["newEtus"]["niveau_id"])->get()) as $matiere) {
        //     if($matiere->)
        // }


    }

    public function generate($parcour_id, $type_id1, $type_id2, $niveau_id, $type_payment_id, $annee_universitaire_id, $etat)
    {
        // $parcours = Parcour::find($parcour_id);
        // $parcours1 = Parcour::find($type_id1);
        // $niveau = Niveau::find($niveau_id);
        // $TypePayment = TypePayment::find($type_payment_id);
        // $this->currentPage = PAGECREATEFORM;
        // $etus = Note::with(['user', 'ue'])->whereHas('user', fn ($q) => $q->whereParcourId($parcour_id)->whereNiveauId($niveau_id))
        //     ->whereHas('ue', fn ($q) => $q->whereTypePaymentId($type_payment_id))->get();

        // $this->current_type = TypePayment::find($parcour_id);
        // if ($type_id1 != 0) {
        //     $this->current_type1 = TypePayment::find($type_id1);
        // }
        // if ($type_id2 != 0) {
        //     $this->current_type2 = TypePayment::find($type_id2);
        // }

        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_type = TypePayment::find($type_payment_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);


        if ($this->type1 == 0 && $this->type2 == 0) {

            if ($etat == 1) {
                if ($parcour_id == 100) {
                    $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))
                        ->whereDoesntHave('historique', fn ($q) => $q->whereTypePaymentId($type_payment_id))
                        ->orderBy('nom')
                        ->get(['id', 'nom', 'prenom']);
                } else {
                    $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))->whereParcourId($parcour_id)
                        ->whereDoesntHave('historique', fn ($q) => $q->whereTypePaymentId($type_payment_id))
                        ->orderBy('nom')
                        ->get(['id', 'nom', 'prenom']);
                }
            } elseif ($etat == 2) {
                if ($parcour_id == 100) {
                    $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))
                        ->whereHas('historique', fn ($q) => $q->whereTypePaymentId($type_payment_id))
                        ->orderBy('nom')
                        ->get(['id', 'nom', 'prenom']);
                } else {
                    $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id)->whereParcourId($parcour_id))
                        ->whereHas('historique', fn ($q) => $q->whereTypePaymentId($type_payment_id))
                        ->orderBy('nom')
                        ->get(['id', 'nom', 'prenom']);
                }
            }

            // dd($this->etus);
        } else if ($this->type1 != 0 && $this->type2 == 0) {

            $this->etus = User::whereParcourId($parcour_id)
                ->whereNiveauId($niveau_id)
                // ->whereDoesntHave('historique', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->where(function($query) {
                //     $query->where('type_payment_id', $this->current_type->id)
                //         ->orWhere('type_payment_id', $this->type1);
                //     }))
                ->whereDoesntHave('historique', fn ($q) => $q->whereTypePaymentId($type_payment_id)->whereAnneeUniversitaireId($annee_universitaire_id))
                ->orWhereDoesntHave('historique', fn ($q) => $q->whereTypePaymentId($this->type1)->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom'])->sortByDesc('moyenne');
        } else if ($this->type1 != 0 && $this->type2 != 0) {
            $this->etus = User::where(function ($query) {
                $query->where('parcour_id', $this->current_parcours->id)
                    ->orWhere('parcour_id', $this->parcour1)
                    ->orWhere('parcour_id', $this->parcour2);
            })
                ->whereNiveauId($niveau_id)
                ->whereHas('etus.ue', fn ($q) => $q->whereTypePaymentId($type_payment_id)->whereAnneeUniversitaireId($annee_universitaire_id))
                ->get(['id', 'nom', 'prenom'])->sortByDesc('moyenne');
        }




        // if($type_id1 != 0){
        //     foreach ($etus as $etu) {
        //         array_push($this->etus, ["nom" => $etu->nom, "prenom" => $etu->prenom, "moyenne" => $etu->moyenne, "parcours" => $parcours->sigle, "parcours1" => $parcours1->sigle, "niveau" => $niveau->nom, "TypePayment" => $TypePayment->nom]);
        //     }
        // }else{
        //     foreach ($etus as $etu) {
        //         array_push($this->etus, ["nom" => $etu->nom, "prenom" => $etu->prenom, "moyenne" => $etu->moyenne, "parcours" => $parcours->sigle, "niveau" => $niveau->nom, "TypePayment" => $TypePayment->nom]);
        //     }
        // }

        // dd($this->etus);



        // $pdf = Pdf::loadView('pdf.resultat', compact('results'));
        // return $pdf->stream('resultat.pdf');
    }
}
