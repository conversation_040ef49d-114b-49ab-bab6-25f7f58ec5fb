{"__meta": {"id": "X009b58c4e929420a7279c91602113d56", "datetime": "2025-07-01 17:19:56", "utime": 1751379596.047432, "method": "POST", "uri": "/livewire/message/utilisateurs", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751379594.503678, "end": 1751379596.047467, "duration": 1.5437889099121094, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1751379594.503678, "relative_start": 0, "end": 1751379595.39575, "relative_end": 1751379595.39575, "duration": 0.8920719623565674, "duration_str": "892ms", "params": [], "collector": null}, {"label": "Application", "start": 1751379595.396501, "relative_start": 0.8928229808807373, "end": 1751379596.04747, "relative_end": 3.0994415283203125e-06, "duration": 0.6509690284729004, "duration_str": "651ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27760520, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.utilisateurs.index (\\resources\\views\\livewire\\utilisateurs\\index.blade.php)", "param_count": 30, "params": ["users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "errors", "_instance", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/index.blade.php&line=0"}, {"name": "livewire.utilisateurs.liste (\\resources\\views\\livewire\\utilisateurs\\liste.blade.php)", "param_count": 32, "params": ["__env", "app", "errors", "_instance", "users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02477, "accumulated_duration_str": "24.77ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.01157, "duration_str": "11.57ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 46.71}, {"sql": "select * from `media` where `media`.`id` in (84)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00658, "duration_str": "6.58ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 46.71, "width_percent": 26.564}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0045899999999999995, "duration_str": "4.59ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 73.274, "width_percent": 18.53}, {"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where `users`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 91.805, "width_percent": 4.481}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (85, 86, 87, 88, 89, 90, 91, 92, 554, 555)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:234", "connection": "inscriptionimsaa", "start_percent": 96.286, "width_percent": 3.714}]}, "models": {"data": {"App\\Models\\Role": 10, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": 1, "App\\Models\\User": 11}, "count": 22}, "livewire": {"data": {"utilisateurs #CYJU06vnXpc2IFNU82zJ": "array:5 [\n  \"data\" => array:17 [\n    \"currentPage\" => \"liste\"\n    \"newUser\" => []\n    \"editUser\" => []\n    \"mediaItems\" => <PERSON><PERSON>\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#1935\n      #items: array:1 [\n        0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#767\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 84\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 555\n            \"uuid\" => \"5afcb7cc-b37d-496d-823f-34b0d7409948\"\n            \"collection_name\" => \"Releve_ou_Diplome_Bacc\"\n            \"name\" => \"etat_paiement_1747034417\"\n            \"file_name\" => \"etat_paiement_1747034417.pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 89752\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2025-05-12 15:33:52\"\n            \"updated_at\" => \"2025-05-12 15:33:52\"\n          ]\n          #original: array:18 [\n            \"id\" => 84\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 555\n            \"uuid\" => \"5afcb7cc-b37d-496d-823f-34b0d7409948\"\n            \"collection_name\" => \"Releve_ou_Diplome_Bacc\"\n            \"name\" => \"etat_paiement_1747034417\"\n            \"file_name\" => \"etat_paiement_1747034417.pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 89752\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2025-05-12 15:33:52\"\n            \"updated_at\" => \"2025-05-12 15:33:52\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n      +collectionName: null\n      +formFieldName: null\n    }\n    \"isEtu\" => false\n    \"query\" => \"\"\n    \"newPasswd\" => null\n    \"filterRole\" => \"\"\n    \"filterStatus\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"selectedUsers\" => []\n    \"selectAll\" => false\n    \"rolePermissions\" => array:2 [\n      \"roles\" => array:7 [\n        0 => array:3 [\n          \"role_id\" => 1\n          \"role_nom\" => \"superadmin\"\n          \"active\" => false\n        ]\n        1 => array:3 [\n          \"role_id\" => 2\n          \"role_nom\" => \"enseignant\"\n          \"active\" => false\n        ]\n        2 => array:3 [\n          \"role_id\" => 3\n          \"role_nom\" => \"deraq\"\n          \"active\" => false\n        ]\n        3 => array:3 [\n          \"role_id\" => 4\n          \"role_nom\" => \"secretaire\"\n          \"active\" => false\n        ]\n        4 => array:3 [\n          \"role_id\" => 5\n          \"role_nom\" => \"etudiant\"\n          \"active\" => true\n        ]\n        5 => array:3 [\n          \"role_id\" => 6\n          \"role_nom\" => \"admin\"\n          \"active\" => false\n        ]\n        6 => array:3 [\n          \"role_id\" => 7\n          \"role_nom\" => \"caf\"\n          \"active\" => false\n        ]\n      ]\n      \"permissions\" => []\n    ]\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"utilisateurs\"\n  \"view\" => \"livewire.utilisateurs.index\"\n  \"component\" => \"App\\Http\\Livewire\\Utilisateurs\"\n  \"id\" => \"CYJU06vnXpc2IFNU82zJ\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/habilitations/download/555/Re<PERSON><PERSON>_<PERSON>_Diplome_Bacc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]"}, "request": {"path_info": "/livewire/message/utilisateurs", "status_code": "<pre class=sf-dump id=sf-dump-2100623400 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2100623400\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1042635572 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1042635572\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-665492757 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">CYJU06vnXpc2IFNU82zJ</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">utilisateurs</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"26 characters\">habilitations/utilisateurs</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">f9066a44</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:17</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">edit</span>\"\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => <span class=sf-dump-note>array:36</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>555</span>\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Katell</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Schmidt</span>\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1994-11-11</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>ville</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>pays</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+261325551650</span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>nom_pere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_mere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1972-04-24</span>\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>duplicata</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>matricule</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>photo</span>\" => \"<span class=sf-dump-str title=\"25 characters\">media/avatars/avatar0.jpg</span>\"\n        \"<span class=sf-dump-key>inscription_date</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-12T12:33:50.000000Z</span>\"\n        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-12T12:33:50.000000Z</span>\"\n        \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_filled</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>tel_pere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_mere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_tuteur</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_tuteur</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"5 characters\">21314</span>\"\n        \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"8 characters\">13241234</span>\"\n        \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">132423</span>\"\n        \"<span class=sf-dump-key>mention_id</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n      \"<span class=sf-dump-key>mediaItems</span>\" => []\n      \"<span class=sf-dump-key>isEtu</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>newPasswd</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filterRole</span>\" => \"\"\n      \"<span class=sf-dump-key>filterStatus</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>selectedUsers</span>\" => []\n      \"<span class=sf-dump-key>selectAll</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>rolePermissions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>roles</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>4</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>permissions</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mediaItems</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Media</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>84</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => \"<span class=sf-dump-str title=\"71 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2bb27a908e6905e3e195059866e45b95240842bfa682feaebab82b20e5c017bf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">kylr</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">goToListUser</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665492757\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-257362176 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2138</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257362176\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1861773788 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56827</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2138</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2138</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/habilitations/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNOQStGdDdtMW1TcnBnVHdRS0pza2c9PSIsInZhbHVlIjoiUUgzKzA1dFQ0ZEEzeThBd1Y5VEFLQVp4ZG43bEdFWGNPNEM4VnBEeEU3VVErZ2syTThiY0xuQ1Z2aTMyRnNJdGpqemRoS3dPSW9DVy8vMUloZWQzd3NrUzRkemxjamlGcm9EUDY5RHo1YzN0M3l3YnFOeDF0bjZiQzdlbDh1cXgiLCJtYWMiOiI5YWYxOGQzNzYxYTBlNTNiZjNiMDJiNGE2MTk5YWQ2ZGY5M2EyNzMzNjlhODM3NjllYzNlY2EwNzI5Y2JhYzdjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImJObldDODA5ZVM1ejNrdnZtUjRIcHc9PSIsInZhbHVlIjoiRkpjMURrVkwxY1MySVJUVnMza3NMUEVTajl1VmdVcjFKYXd5c3JvcVJTZXNaendyb2Y3RXBBeU94dThPL0NmOEROdlZtNmNyTGZpWDVnZ1hpRE15MzJkZDBlQlI3NHNJSGhRU2hOdzZjZ1N6MHlQNE5sU3Erakc3NC83czdjQVgiLCJtYWMiOiJkODk3OGQxNmRiZTdlZmU4NWM5OTRmODg5N2NjMDM5ZjAxMDdiNDIzZWQ5MGY3NjQ0NDAwNzk1YWFmOWNjYmNmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751379594.5037</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751379594</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861773788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-933611128 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933611128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-372507023 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:19:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtoT2tqSDhnUFVMZXFWSGRsY2t2TXc9PSIsInZhbHVlIjoiMmNmaC9XREFDV3gvQWl4Y3JJbk9aYXg1dlgxdHVUdjNhZElyZ3N6K3cwelp3eHJZaXI2aDBOdEh6T2tDN1pjS2MvUTJ1VXgwNDltVzdCaUt1MkJ3NEpNN3ZML3U1OXhYZHhoLzEwd1JBaTROQThwZC9hendIeHdWMmo0c1RNVUkiLCJtYWMiOiJmMzdiNDg5OTdlMDhmMDVmMzIyMjEyMGUwYWY0MTIzYmUyYTk3M2YyMjUxZjA2Y2Q1ZTAwNzQ4MTVjNTA3ODhmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6InNlOXQyOUloeHNYOGdIbVg0TW1aQ3c9PSIsInZhbHVlIjoidWV3cmZjdjBGejV2Q2VadmhCWktzYklNakpZOVZUK2MrVTVyMlNsT3JnYlpUTnB2azE1SDlHaU1VbVZqVUNRNnVoQUZxVER4N3lXYVlGZm5JblRrZmNOYUdMRitBMGV5N0s4bExYNXo5VXUyZ1kzQTRiUWNDNS9tcjhmRGx0aG8iLCJtYWMiOiJhYjA1MmIwNWFlMjQxZTVmYTcxYzJmOGQ0MzZlNjU5YTZiY2E0Y2VkYWRiYmZjZWUyZDRkNjZlMzZjMTVjNjYyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:19:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtoT2tqSDhnUFVMZXFWSGRsY2t2TXc9PSIsInZhbHVlIjoiMmNmaC9XREFDV3gvQWl4Y3JJbk9aYXg1dlgxdHVUdjNhZElyZ3N6K3cwelp3eHJZaXI2aDBOdEh6T2tDN1pjS2MvUTJ1VXgwNDltVzdCaUt1MkJ3NEpNN3ZML3U1OXhYZHhoLzEwd1JBaTROQThwZC9hendIeHdWMmo0c1RNVUkiLCJtYWMiOiJmMzdiNDg5OTdlMDhmMDVmMzIyMjEyMGUwYWY0MTIzYmUyYTk3M2YyMjUxZjA2Y2Q1ZTAwNzQ4MTVjNTA3ODhmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6InNlOXQyOUloeHNYOGdIbVg0TW1aQ3c9PSIsInZhbHVlIjoidWV3cmZjdjBGejV2Q2VadmhCWktzYklNakpZOVZUK2MrVTVyMlNsT3JnYlpUTnB2azE1SDlHaU1VbVZqVUNRNnVoQUZxVER4N3lXYVlGZm5JblRrZmNOYUdMRitBMGV5N0s4bExYNXo5VXUyZ1kzQTRiUWNDNS9tcjhmRGx0aG8iLCJtYWMiOiJhYjA1MmIwNWFlMjQxZTVmYTcxYzJmOGQ0MzZlNjU5YTZiY2E0Y2VkYWRiYmZjZWUyZDRkNjZlMzZjMTVjNjYyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:19:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372507023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1148958141 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/habilitations/download/555/Releve_ou_Diplome_Bacc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148958141\", {\"maxDepth\":0})</script>\n"}}