<?php

namespace App\Http\Livewire;

use Carbon\Carbon;
use Livewire\Component;

class Counter extends Component
{
    public $count = 0;

    public function increment()
    {
        $this->count++;
    }

    public function render()
    {
        Carbon::setLocale("fr");
        
        return view('livewire.counter');
    }

    public function getMoyenneAttribute()    
    {
        
        $sum= 0;
        $count= 0;
        foreach($this->notes as$note) {
            $sum+= $note->valeur;
            $count++;
        }
        return $count>0 ? $sum/ $count: 0;
    }

}
