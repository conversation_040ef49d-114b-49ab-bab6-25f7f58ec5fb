<?php

namespace App\Http\Livewire;

use App\Models\Niveau;
use App\Models\TypePayment;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class TypePay extends Component
{
    public $currentPage = PAGELIST;

    public $newPay = [];

    public $editPay = [];
    public $prix = [];
    
    public function render()
    {
        return view('livewire.administration.typepay.index', [
            "pays" => TypePayment::with('niveau')->get(),
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editPay.nom' => 'required',
                'editPay.prix' => 'required',
            ];
        }

        return [
            'newPay.nom' => 'required',
            'newPay.prix' => 'required',
            
        ];
    }

    public function goToListPay()
    {
        $this->currentPage = PAGELIST;
        $this->newPay = [];
    }

    public function goToAddPay()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditPay(TypePayment $Pay){
        $this->editPay = $Pay->toArray();
        $this->populateNiveau();

        $this->currentPage = PAGEEDITFORM;
    }

    public function populateNiveau()
    {
        $this->prix = [];

        $mapForCB = function ($value) {
            return $value["id"];
        };

        $niveauIds = array_map($mapForCB, TypePayment::find($this->editPay["id"])->niveau->toArray()); // [1, 2, 4]

        foreach (Niveau::all() as $niveau) {
            if (in_array($niveau->id, $niveauIds)) {
                array_push($this->prix, ["niveau_id" => $niveau->id, "niveau_nom" => $niveau->nom, "active" => true, "prix" => TypePayment::find($this->editPay["id"])->niveau()->whereNiveauId($niveau->id)->first()->pivot->prix]);
            } else {
                array_push($this->prix, ["niveau_id" => $niveau->id, "niveau_nom" => $niveau->nom, "active" => false, "prix" => 0]);
            }
        }

        // dd($this->prix);

        // la logique pour charger les roles et les permissions
    }

    public function updateNiveauPrice()
    {
        
        DB::table("niveau_type_payment")->where("type_payment_id", $this->editPay["id"])->delete();

        foreach ($this->prix as $pr) {
            if ($pr["active"]) {
                TypePayment::find($this->editPay["id"])->niveau()->attach([$pr["niveau_id"] => ['prix' => $pr["prix"]]]);
            }
        }

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Roles et permissions mis à jour avec succès!"]);
    }

    public function addPay()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        TypePayment::create($validationAttributes["newPay"]);

        $this->newPay = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Pay créé avec succès!"]);
        $this->goToListPay();
    }

    public function updatePay(){
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        TypePayment::find($this->editPay["id"])->update($validationAttributes["editPay"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Pay mis à jour avec succès!"]);
        $this->goToListPay();
    }

    public function deletePay($id)
    {
        TypePayment::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Pay supprimé avec succès!"]);
    }
}
