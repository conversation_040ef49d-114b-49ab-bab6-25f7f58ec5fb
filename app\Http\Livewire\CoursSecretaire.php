<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Ue;
use App\Models\User;
use Illuminate\Contracts\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class CoursSecretaire extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;
    public $query;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreAnnee;

    public function updatingQuery(){
        $this->resetPage();
    }
    public function updatingFiltreAnnee(){
        $this->resetPage();
    }
    public function updatingFiltreParcours(){
        $this->resetPage();
    }
    public function updatingFiltreNiveau(){
        $this->resetPage();
    }

    public function render()
    {
        $persQuery = Matiere::query()->with(['user', 'ue']);

        if($this->query != ""){
            $persQuery->where(function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreParcours != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        if($this->filtreNiveau != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }
        if($this->filtreAnnee != ""){
            $persQuery->whereHas('ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }

        return view('livewire.secretaire.cours.index', [
            "cours" => $persQuery->latest()->paginate(5),
            "enseignants" => User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
            ->get(['id', 'nom', 'prenom']),
            "niveaux" => Niveau::all(),
            "parcours" => Parcour::all(),
            "annees" => AnneeUniversitaire::all()
            // "ues" => Ue::all(['id', 'code', 'nom'])
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

}
