<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\TypeNote;
use Livewire\Component;
use Livewire\WithPagination;

class NoteStatus extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editNote = [];
    public $query;
    public $filtreAnnee;
    public $filtreParcours;
    public $filtreNiveau;


    public function updatingQuery()
    {
        $this->resetPage();
    }
    public function updatingFiltreAnnee()
    {
        $this->resetPage();
    }
    public function updatingFiltreParcours()
    {
        $this->resetPage();
    }
    public function updatingFiltreNiveau()
    {
        $this->resetPage();
    }

    public function render()
    {

        $persQuery = Matiere::query()->with(['note']);

        if ($this->query != "") {
            $persQuery->where(function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('code', 'like', '%' . $this->query . '%');
            });
        }
        if ($this->filtreParcours != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        if ($this->filtreNiveau != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }

        if ($this->filtreAnnee != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }

        return view('livewire.deraq.status.index', [
            "status" => $persQuery->latest()->paginate(10),
            "annees" => AnneeUniversitaire::all(),
            "typeNotes" => TypeNote::all(),
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }


    public function ajoutNote($matiere, $typeNoteActu, $typeNoteAjout)
    {

        $notes = Note::whereMatiereId($matiere)
            ->whereTypeNoteId($typeNoteAjout)
            ->get();

        if ($notes->isNotEmpty()) {
            $historyNote = HistoryNote::create([
                "matiere_id" => $matiere,
                "type_note_id" => $typeNoteActu,
                "user_id" => auth()->user()->id,
            ]);


            foreach ($notes as $note) {

                Note::create([
                    "type_note_id" => $typeNoteActu,
                    "valeur" => $note->valeur,
                    "user_id" => $note->user->id,
                    "matiere_id" => $matiere,
                    "history_note_id" => $historyNote->id,
                    "observation" => $note->observation,
                ]);
            }


            $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note ajouté avec succès!"]);
            $this->render();
        }else{
            $this->dispatchBrowserEvent("showErrorMessage", ["message" => "Pas de note!"]);
        }
    }
}
