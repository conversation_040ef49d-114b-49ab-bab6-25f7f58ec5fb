<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Domaine;
use App\Models\Mention;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use Livewire\Component;
use Livewire\WithPagination;

class Mentions extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newMention = [];
    public $editMention = [];
    public $query;
    public $filtreDomaine;
    public $filtreNiveau;

    public function updatingQuery(){
        $this->resetPage();
    }

    public function render()
    {
        $persQuery = Mention::query()->with('domaine');

        if($this->query != ""){
            $persQuery->where(function($query) {
                $query->where('nom', 'like', '%'. $this->query .'%')
                    ->orWhere('code', 'like', '%'. $this->query .'%');
            });
        }

        if($this->filtreDomaine != ""){
            $persQuery->whereDomaineId($this->filtreDomaine);
        }
        if($this->filtreNiveau != ""){
            $persQuery->whereNiveauId($this->filtreNiveau);
        }

        return view('livewire.deraq.mention.index', [
            "mentions" => $persQuery->paginate(15),
            "domaines" => Domaine::all(),
            "niveaux" => Niveau::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editMention.nom' => 'required',
                'editMention.domaine_id' => 'required',
            ];
        }

        return [
            'newMention.nom' => 'required',
            'newMention.domaine_id' => 'required',

        ];
    }

    public function goToListMention()
    {
        $this->currentPage = PAGELIST;
        $this->newMention = [];
    }

    public function goToAddMention()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function goToEditMention(Mention $Mention){
        $this->editMention = $Mention->toArray();
        $this->currentPage = PAGEEDITFORM;
    }

    public function addMention()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur
        Mention::create($validationAttributes["newMention"]);

        $this->newMention = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Mention créé avec succès!"]);
        $this->goToListMention();
    }

    public function updateMention(){
        // Vérifier qMention les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();


        Mention::find($this->editMention["id"])->update($validationAttributes["editMention"]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message"=>"Mention mis à jour avec succès!"]);
        $this->goToListMention();
    }

    public function deleteMention($id)
    {
        Mention::destroy($id);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Mention supprimé avec succès!"]);
    }
}
