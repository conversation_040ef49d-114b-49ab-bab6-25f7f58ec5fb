<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\View;
use Livewire\Component;
use Livewire\WithPagination;

class Releve extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;
    
    public $newCours = [];
    public $editCours = [];
    
    public User $current_user;
    public Parcour $current_parcours;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public $persQuery;

    // public $parcours;
    // public $parcour;

    // public $ues;
    // public $ue;


    public function mount($userId, $parcourId, $niveauId, $anneeId)
    {
        $this->current_user = User::find($userId);
        $this->current_parcours = Parcour::find($parcourId);
        $this->current_niveau = Niveau::find($niveauId);
        $this->current_annee = AnneeUniversitaire::find($anneeId);

        $this->persQuery = Semestre::query()
        ->withWhereHas('ue', fn ($q) => $q->whereParcourId($this->current_parcours->id)
                                        ->whereNiveauId($this->current_niveau->id)
                                        ->whereAnneeUniversitaireId($this->current_annee->id)
                                        ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($this->current_user->id))
                                        ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($this->current_user->id))
                                        ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($this->current_user->id)))
        ->get();

        // $this->persQuery = Ue::query()->with(['matiere'])
        // ->whereParcourId($parcourId)
        // ->whereNiveauId($niveauId)
        // ->whereAnneeUniversitaireId($anneeId)
        // ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($userId))
        // ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($userId))
        // ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($userId))
        // ->get();
    }

    public function render()
    {
        // $persQuery = Matiere::query()->with(['ue'])
        // ->whereHas('ue', fn ($q) => $q->whereParcourId($this->current_parcours->id)
        //                                 ->whereNiveauId($this->current_niveau->id)
        //                                 ->whereAnneeUniversitaireId($this->current_annee->id))
        // ->withWhereHas('notesP1', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->withWhereHas('notesP2', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->withWhereHas('notesExam', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->get();

        // $persQuery = Ue::query()->with(['matiere'])
        // ->whereParcourId($this->current_parcours->id)
        // ->whereNiveauId($this->current_niveau->id)
        // ->whereAnneeUniversitaireId($this->current_annee->id)
        // ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($this->current_user->id))
        // ->get();

        // dd($this->persQuery);

        return view('livewire.deraq.releve.index', [
            "semestres" => $this->persQuery,
            // "ues" => Ue::all(['id', 'code', 'nom'])
        ])
        ->extends('layouts.backend')
        ->section('content');

        
    }

    public function pdfGenerate()
    {
        $e = Semestre::query()
        ->withWhereHas('ue', fn ($q) => $q->whereParcourId($this->current_parcours->id)
                                        ->whereNiveauId($this->current_niveau->id)
                                        ->whereAnneeUniversitaireId($this->current_annee->id)
                                        ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($this->current_user->id))
                                        ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($this->current_user->id))
                                        ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($this->current_user->id)))
        ->get();
        // dd($e);
        $view     = View::make('pdf.releve', ['semestres' => $e, 'current_user' => $this->current_user, 'current_parcours' => $this->current_parcours, 'current_niveau' => $this->current_niveau, 'current_annee' => $this->current_annee]);
        $html     = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');
        // dd($view);
        $pdfContent = PDF::loadHtml($html)->output();
        return response()->streamDownload(
            fn () => print($pdfContent),
            "relevé " . $this->current_user->nom . ".pdf"
        );
        
        // $pdfContent = PDF::loadView('pdf.releve', ['semestres' => $this->persQuery])->output();
        // return response()->streamDownload(
        //     fn () => print($pdfContent),
        //     "filename.pdf"
        // );
    }
}
